[] 根据HI10的模块文档，实现`每一条`指令并进行测试
   优先实现并测试以下功能：
   识别（识别过程中的notify信息展示）
   NOTIFY
   OTA（包含动态调整波特率）
   抓图（两种方式，可以将目前代码的snap和capture对应修改为，抓原图和抓算法图）
   存图

[] 界面的简化。目前界面依然延续Facelock的界面，需要简化。
   使用Qtdesinger修改界面，然后使用ui/generate_ui.sh生成代码
   界面上全部用英文，然后通过国际化方案实现中文的显示，参考doc/localization.md
   界面上的所有元素都需要检查，注意将原有的facelock相关的文字和命令修改为HI10的文字和命令

[] 代码的整理，去除facelock相关的代码，修改为HI10相关的描述

[] 修改release.bat release.sh完成软件的打包（需要win和linux均能使用）




[x]翻译


[x]抓图按钮拆分


[x]ui优化
[x]hi10win 解耦


[X] 保存日志文件，图片到工具目录

[x] 保存png到文件



[x]同时保存raw和png
[x]同时兼容yuv和raw

[x]可以配置客户选项和内部参数,隐藏软件版本修改点，以及其他不需要的按钮
[]支持发送自定义cmd，指令CmdCode EXEC_SHELL_CMDS XNN_WGT_CHECKSUM_VERIFY = 11 先reboot后debug
[]收到ready验证，设置为选项
[]发送文件
[] 设置阈值修改成0-4

 
