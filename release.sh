#! /bin/bash

# release script for HI10 app
version='v1.0.0_'
time=$(date "+%Y_%m_%d")
hi10_tool_version_str="hi10_tool_version:v1.0.0-${time}"
commit_id=`git rev-parse --short HEAD`

use_rar_package='false'  # Simplified - no rar packaging
app_name='HI10_Tool'

logo_path="resource/logo.ico"
# HI10 Tool configuration
if [ -n "$1" ] && [ "$1" == "hi10" ] ; then
    appfile='HI10Win.py'
    app_name="HI10_Tool_"${hi10_tool_version_str#hi10_tool_version:}
    echo $hi10_tool_version_str > ./version_str
    echo "app_name:"$app_name >> ./version_str
    extra_package_cmds="-p $(pwd)/src/"
else
    # Default: HI10 Tool
    appfile='HI10Win.py'
    app_name="HI10_Tool_"$version$time
    echo "HI10_Tool_version:"${version}${commit_id} > ./version_str
    echo "app_name:"$app_name >> ./version_str
    extra_package_cmds="-p $(pwd)/src/"
fi

# Check if required files exist
echo "Checking required files..."
if [ ! -f "localization/hi10_app.qm" ]; then
    echo "Warning: Translation file localization/hi10_app.qm not found!"
    echo "The application may not have Chinese translation support."
fi
if [ ! -f "resource/logo.ico" ]; then
    echo "Warning: Logo file resource/logo.ico not found!"
fi
if [ ! -f "ui/MainWindow.py" ]; then
    echo "Warning: UI file ui/MainWindow.py not found!"
fi

# Build the application
echo "Building HI10 Tool..."
echo "App file: $appfile"
echo "App name: $app_name"
echo "Logo path: $logo_path"

# Build with comprehensive package options
echo "Building application with all dependencies..."

if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux build
    python -m PyInstaller \
        --hidden-import=pyttsx3.drivers \
        --hidden-import=pyttsx3.drivers.dummy \
        --hidden-import=pyttsx3.drivers.espeak \
        --hidden-import=pyttsx3.drivers.nsss \
        --hidden-import=pyttsx3.drivers.sapi5 \
        --hidden-import=PyQt5.QtCore \
        --hidden-import=PyQt5.QtWidgets \
        --hidden-import=PyQt5.QtGui \
        --hidden-import=qasync \
        --hidden-import=serial \
        --hidden-import=numpy \
        --add-data=version_str:. \
        --add-data=resource:resource \
        --add-data=localization/hi10_app.qm:localization \
        --add-data=localization/hi10_app.ts:localization \
        --add-data=ui:ui \
        $extra_package_cmds \
        -F -w "$appfile" \
        -n "$app_name"
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
    # Windows build (using Linux-style paths in MSYS/Cygwin)
    python -m PyInstaller \
        --hidden-import=pyttsx3.drivers \
        --hidden-import=pyttsx3.drivers.dummy \
        --hidden-import=pyttsx3.drivers.espeak \
        --hidden-import=pyttsx3.drivers.nsss \
        --hidden-import=pyttsx3.drivers.sapi5 \
        --hidden-import=pynput.keyboard._win32 \
        --hidden-import=PyQt5.QtCore \
        --hidden-import=PyQt5.QtWidgets \
        --hidden-import=PyQt5.QtGui \
        --hidden-import=qasync \
        --hidden-import=serial \
        --hidden-import=numpy \
        --add-data=version_str:. \
        --add-data=resource:resource \
        --add-data=localization/hi10_app.qm:localization \
        --add-data=localization/hi10_app.ts:localization \
        --add-data=ui:ui \
        $extra_package_cmds \
        -F -w "$appfile" \
        -n "$app_name"
else
    echo "Unsupported OS: $OSTYPE"
    exit 1
fi

# Check if build was successful
if [ $? -ne 0 ]; then
    echo ""
    echo "Build failed with error code $?"
    exit $?
fi

# Clean up
echo "Cleaning up build files..."
rm -f *.spec
rm -rf build/*

echo ""
echo "========================================"
echo "Build completed successfully!"
echo "========================================"
echo "Executable: dist/$app_name"

# Check if executable exists and show details
if [ -f "dist/$app_name" ]; then
    echo ""
    echo "Executable size:"
    ls -lh "dist/$app_name" | awk '{print "  " $5 " bytes"}'
    echo ""
    echo "Files included in the package:"
    echo "  - Python runtime and all dependencies"
    echo "  - PyQt5 GUI framework"
    echo "  - Translation files (Chinese support)"
    echo "  - Application resources (icons, UI files)"
    echo "  - Version information"
    echo ""
    echo "The executable is completely self-contained."
    echo "Users do NOT need to install Python or any dependencies."
    echo ""
    echo "To run: ./dist/$app_name"
else
    echo ""
    echo "ERROR: Executable not found in dist folder!"
    echo "Build may have failed."
    exit 1
fi
