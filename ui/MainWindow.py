# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'MainWindow.ui'
#
# Created by: PyQt5 UI code generator 5.15.11
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(960, 653)
        MainWindow.setMinimumSize(QtCore.QSize(960, 653))
        MainWindow.setMaximumSize(QtCore.QSize(16777215, 16777215))
        MainWindow.setStyleSheet("background-color: rgb(195, 229, 229);")
        self.container = QtWidgets.QWidget(MainWindow)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.container.sizePolicy().hasHeightForWidth())
        self.container.setSizePolicy(sizePolicy)
        self.container.setMinimumSize(QtCore.QSize(960, 625))
        self.container.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.container.setStyleSheet("background-color: rgb(195, 229, 229);")
        self.container.setObjectName("container")
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout(self.container)
        self.horizontalLayout_14.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_14.setSpacing(0)
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        spacerItem = QtWidgets.QSpacerItem(10, 20, QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_14.addItem(spacerItem)
        self.widget = QtWidgets.QWidget(self.container)
        self.widget.setMinimumSize(QtCore.QSize(940, 625))
        self.widget.setStyleSheet("")
        self.widget.setObjectName("widget")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.widget)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setSpacing(0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        spacerItem1 = QtWidgets.QSpacerItem(20, 10, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Preferred)
        self.verticalLayout_2.addItem(spacerItem1)
        self.widget_2 = QtWidgets.QWidget(self.widget)
        self.widget_2.setMinimumSize(QtCore.QSize(940, 30))
        self.widget_2.setStyleSheet("background-color: rgb(211, 190, 230);")
        self.widget_2.setObjectName("widget_2")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.widget_2)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setSpacing(0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.port_label = QtWidgets.QLabel(self.widget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.port_label.sizePolicy().hasHeightForWidth())
        self.port_label.setSizePolicy(sizePolicy)
        self.port_label.setMinimumSize(QtCore.QSize(70, 30))
        self.port_label.setMaximumSize(QtCore.QSize(70, 16777215))
        self.port_label.setAlignment(QtCore.Qt.AlignCenter)
        self.port_label.setObjectName("port_label")
        self.horizontalLayout_3.addWidget(self.port_label)
        self.widget_5 = QtWidgets.QWidget(self.widget_2)
        self.widget_5.setMinimumSize(QtCore.QSize(870, 30))
        self.widget_5.setStyleSheet("background-color: rgb(238, 238, 236);\n"
"")
        self.widget_5.setObjectName("widget_5")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.widget_5)
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_9.setSpacing(0)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_7.setSizeConstraint(QtWidgets.QLayout.SetDefaultConstraint)
        self.horizontalLayout_7.setSpacing(0)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.port_combobox = QtWidgets.QComboBox(self.widget_5)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.port_combobox.sizePolicy().hasHeightForWidth())
        self.port_combobox.setSizePolicy(sizePolicy)
        self.port_combobox.setMinimumSize(QtCore.QSize(330, 30))
        self.port_combobox.setObjectName("port_combobox")
        self.horizontalLayout_7.addWidget(self.port_combobox)
        spacerItem2 = QtWidgets.QSpacerItem(5, 20, QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_7.addItem(spacerItem2)
        self.baudrate_combobox = QtWidgets.QComboBox(self.widget_5)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.baudrate_combobox.sizePolicy().hasHeightForWidth())
        self.baudrate_combobox.setSizePolicy(sizePolicy)
        self.baudrate_combobox.setMinimumSize(QtCore.QSize(330, 30))
        self.baudrate_combobox.setObjectName("baudrate_combobox")
        self.horizontalLayout_7.addWidget(self.baudrate_combobox)
        self.horizontalLayout_7.setStretch(0, 270)
        self.horizontalLayout_7.setStretch(1, 5)
        self.horizontalLayout_7.setStretch(2, 270)
        self.horizontalLayout_9.addLayout(self.horizontalLayout_7)
        spacerItem3 = QtWidgets.QSpacerItem(5, 20, QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem3)
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setSizeConstraint(QtWidgets.QLayout.SetDefaultConstraint)
        self.horizontalLayout_4.setSpacing(0)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.connect_btn = QtWidgets.QPushButton(self.widget_5)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.connect_btn.sizePolicy().hasHeightForWidth())
        self.connect_btn.setSizePolicy(sizePolicy)
        self.connect_btn.setMinimumSize(QtCore.QSize(100, 30))
        self.connect_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.connect_btn.setStyleSheet("#connect_btn{\n"
"color: rgb(0, 0, 0);\n"
"background-color: rgb(247, 255, 255);\n"
"}\n"
"#connect_btn:hover{\n"
"color: rgb(0, 120, 235);\n"
"background-color: rgb(238, 245, 245);\n"
"}\n"
"#connect_btn:pressed{\n"
"color: rgb(20, 220, 200);\n"
"background-color: rgb(229, 236, 236);\n"
"}")
        self.connect_btn.setObjectName("connect_btn")
        self.horizontalLayout_4.addWidget(self.connect_btn)
        self.disconnect_btn = QtWidgets.QPushButton(self.widget_5)
        self.disconnect_btn.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.disconnect_btn.sizePolicy().hasHeightForWidth())
        self.disconnect_btn.setSizePolicy(sizePolicy)
        self.disconnect_btn.setMinimumSize(QtCore.QSize(100, 30))
        self.disconnect_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.disconnect_btn.setStyleSheet("#disconnect_btn{\n"
"color: rgb(0, 0, 0);\n"
"background-color: rgb(247, 255, 255);\n"
"}\n"
"#disconnect_btn:hover{\n"
"color: rgb(0, 120, 235);\n"
"background-color: rgb(238, 245, 245);\n"
"}\n"
"#disconnect_btn:pressed{\n"
"color: rgb(20, 220, 200);\n"
"background-color: rgb(229, 236, 236);\n"
"}")
        self.disconnect_btn.setObjectName("disconnect_btn")
        self.horizontalLayout_4.addWidget(self.disconnect_btn)
        self.horizontalLayout_9.addLayout(self.horizontalLayout_4)
        self.horizontalLayout_9.setStretch(0, 665)
        self.horizontalLayout_9.setStretch(1, 5)
        self.horizontalLayout_9.setStretch(2, 200)
        self.horizontalLayout_3.addWidget(self.widget_5)
        self.horizontalLayout_3.setStretch(0, 70)
        self.horizontalLayout_3.setStretch(1, 870)
        self.verticalLayout_2.addWidget(self.widget_2)
        spacerItem4 = QtWidgets.QSpacerItem(20, 5, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Preferred)
        self.verticalLayout_2.addItem(spacerItem4)
        self.widget_3 = QtWidgets.QWidget(self.widget)
        self.widget_3.setMinimumSize(QtCore.QSize(940, 100))
        self.widget_3.setStyleSheet("background-color: rgb(241, 246, 207);")
        self.widget_3.setObjectName("widget_3")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.widget_3)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setSpacing(0)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.functions_label = QtWidgets.QLabel(self.widget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.functions_label.sizePolicy().hasHeightForWidth())
        self.functions_label.setSizePolicy(sizePolicy)
        self.functions_label.setMinimumSize(QtCore.QSize(70, 100))
        self.functions_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.functions_label.setAlignment(QtCore.Qt.AlignCenter)
        self.functions_label.setObjectName("functions_label")
        self.horizontalLayout_8.addWidget(self.functions_label)
        self.widget_4 = QtWidgets.QWidget(self.widget_3)
        self.widget_4.setMinimumSize(QtCore.QSize(870, 100))
        self.widget_4.setStyleSheet("background-color: rgb(238, 238, 236);")
        self.widget_4.setObjectName("widget_4")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.widget_4)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setSizeConstraint(QtWidgets.QLayout.SetDefaultConstraint)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.verticalLayout.addLayout(self.horizontalLayout)
        spacerItem5 = QtWidgets.QSpacerItem(20, 5, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Preferred)
        self.verticalLayout.addItem(spacerItem5)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setSizeConstraint(QtWidgets.QLayout.SetDefaultConstraint)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setSpacing(0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.cont_recognize_checkBox = QtWidgets.QCheckBox(self.widget_4)
        self.cont_recognize_checkBox.setEnabled(False)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.cont_recognize_checkBox.sizePolicy().hasHeightForWidth())
        self.cont_recognize_checkBox.setSizePolicy(sizePolicy)
        self.cont_recognize_checkBox.setMinimumSize(QtCore.QSize(100, 30))
        self.cont_recognize_checkBox.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.cont_recognize_checkBox.setStyleSheet("#cont_recognize_checkBox{\n"
"background-color: rgb(238, 238, 236);\n"
"}\n"
"#cont_recognize_checkBox:hover{\n"
"color: rgb(0, 120, 235);\n"
"background-color: rgb(238, 245, 245);\n"
"}\n"
"#cont_recognize_checkBox:pressed{\n"
"color: rgb(20, 220, 200);\n"
"background-color: rgb(229, 236, 236);\n"
"}")
        self.cont_recognize_checkBox.setObjectName("cont_recognize_checkBox")
        self.horizontalLayout_2.addWidget(self.cont_recognize_checkBox)
        self.recognize_times_lineedit = QtWidgets.QLineEdit(self.widget_4)
        self.recognize_times_lineedit.setEnabled(False)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.recognize_times_lineedit.sizePolicy().hasHeightForWidth())
        self.recognize_times_lineedit.setSizePolicy(sizePolicy)
        self.recognize_times_lineedit.setMinimumSize(QtCore.QSize(50, 30))
        self.recognize_times_lineedit.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.recognize_times_lineedit.setObjectName("recognize_times_lineedit")
        self.horizontalLayout_2.addWidget(self.recognize_times_lineedit)
        spacerItem6 = QtWidgets.QSpacerItem(5, 20, QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem6)
        self.recognize_btn = QtWidgets.QPushButton(self.widget_4)
        self.recognize_btn.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.recognize_btn.sizePolicy().hasHeightForWidth())
        self.recognize_btn.setSizePolicy(sizePolicy)
        self.recognize_btn.setMinimumSize(QtCore.QSize(100, 30))
        self.recognize_btn.setFocusPolicy(QtCore.Qt.StrongFocus)
        self.recognize_btn.setObjectName("recognize_btn")
        self.horizontalLayout_2.addWidget(self.recognize_btn)
        spacerItem7 = QtWidgets.QSpacerItem(5, 20, QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem7)
        self.stop_recognize_btn = QtWidgets.QPushButton(self.widget_4)
        self.stop_recognize_btn.setEnabled(False)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.stop_recognize_btn.sizePolicy().hasHeightForWidth())
        self.stop_recognize_btn.setSizePolicy(sizePolicy)
        self.stop_recognize_btn.setMinimumSize(QtCore.QSize(100, 30))
        self.stop_recognize_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.stop_recognize_btn.setStyleSheet("#stop_recognize_btn{\n"
"background-color: rgb(238, 238, 236);\n"
"}\n"
"#stop_recognize_btn:hover{\n"
"color: rgb(0, 120, 235);\n"
"background-color: rgb(238, 245, 245);\n"
"}\n"
"#stop_recognize_btn:pressed{\n"
"color: rgb(20, 220, 200);\n"
"background-color: rgb(229, 236, 236);\n"
"}")
        self.stop_recognize_btn.setObjectName("stop_recognize_btn")
        self.horizontalLayout_2.addWidget(self.stop_recognize_btn)
        spacerItem8 = QtWidgets.QSpacerItem(5, 20, QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem8)
        self.snap_btn = QtWidgets.QPushButton(self.widget_4)
        self.snap_btn.setEnabled(False)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.snap_btn.sizePolicy().hasHeightForWidth())
        self.snap_btn.setSizePolicy(sizePolicy)
        self.snap_btn.setMinimumSize(QtCore.QSize(100, 30))
        self.snap_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.snap_btn.setStyleSheet("#snap_btn{\n"
"background-color: rgb(238, 238, 236);\n"
"}\n"
"#snap_btn:hover{\n"
"color: rgb(0, 120, 235);\n"
"background-color: rgb(238, 245, 245);\n"
"}\n"
"#snap_btn:pressed{\n"
"color: rgb(20, 220, 200);\n"
"background-color: rgb(229, 236, 236);\n"
"}")
        self.snap_btn.setObjectName("snap_btn")
        self.horizontalLayout_2.addWidget(self.snap_btn)
        spacerItem9 = QtWidgets.QSpacerItem(5, 20, QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem9)
        self.read_raw_btn = QtWidgets.QPushButton(self.widget_4)
        self.read_raw_btn.setEnabled(False)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.read_raw_btn.sizePolicy().hasHeightForWidth())
        self.read_raw_btn.setSizePolicy(sizePolicy)
        self.read_raw_btn.setMinimumSize(QtCore.QSize(100, 30))
        self.read_raw_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.read_raw_btn.setStyleSheet("#read_raw_btn{\n"
"background-color: rgb(238, 238, 236);\n"
"}\n"
"#read_raw_btn:hover{\n"
"color: rgb(0, 120, 235);\n"
"background-color: rgb(238, 245, 245);\n"
"}\n"
"#read_raw_btn:pressed{\n"
"color: rgb(20, 220, 200);\n"
"background-color: rgb(229, 236, 236);\n"
"}")
        self.read_raw_btn.setObjectName("read_raw_btn")
        self.horizontalLayout_2.addWidget(self.read_raw_btn)
        spacerItem10 = QtWidgets.QSpacerItem(5, 20, QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem10)
        self.read_alg_btn = QtWidgets.QPushButton(self.widget_4)
        self.read_alg_btn.setEnabled(False)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.read_alg_btn.sizePolicy().hasHeightForWidth())
        self.read_alg_btn.setSizePolicy(sizePolicy)
        self.read_alg_btn.setMinimumSize(QtCore.QSize(100, 30))
        self.read_alg_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.read_alg_btn.setStyleSheet("#read_alg_btn{\n"
"background-color: rgb(238, 238, 236);\n"
"}\n"
"#read_alg_btn:hover{\n"
"color: rgb(0, 120, 235);\n"
"background-color: rgb(238, 245, 245);\n"
"}\n"
"#read_alg_btn:pressed{\n"
"color: rgb(20, 220, 200);\n"
"background-color: rgb(229, 236, 236);\n"
"}")
        self.read_alg_btn.setObjectName("read_alg_btn")
        self.horizontalLayout_2.addWidget(self.read_alg_btn)
        spacerItem11 = QtWidgets.QSpacerItem(10, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem11)
        self.horizontalLayout_2.setStretch(0, 100)
        self.horizontalLayout_2.setStretch(1, 50)
        self.horizontalLayout_2.setStretch(2, 5)
        self.horizontalLayout_2.setStretch(3, 100)
        self.horizontalLayout_2.setStretch(4, 5)
        self.horizontalLayout_2.setStretch(5, 100)
        self.horizontalLayout_2.setStretch(6, 5)
        self.horizontalLayout_2.setStretch(7, 100)
        self.horizontalLayout_2.setStretch(8, 5)
        self.horizontalLayout_2.setStretch(9, 100)
        self.horizontalLayout_2.setStretch(10, 5)
        self.verticalLayout.addLayout(self.horizontalLayout_2)
        spacerItem12 = QtWidgets.QSpacerItem(20, 5, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Preferred)
        self.verticalLayout.addItem(spacerItem12)
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.send_cmd = QtWidgets.QPushButton(self.widget_4)
        self.send_cmd.setEnabled(False)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.send_cmd.sizePolicy().hasHeightForWidth())
        self.send_cmd.setSizePolicy(sizePolicy)
        self.send_cmd.setMinimumSize(QtCore.QSize(100, 30))
        self.send_cmd.setStyleSheet("#send_cmd{\n"
"background-color: rgb(238, 238, 236);\n"
"}\n"
"#send_cmd:hover{\n"
"color: rgb(0, 120, 235);\n"
"background-color: rgb(238, 245, 245);\n"
"}\n"
"#send_cmd:pressed{\n"
"color: rgb(20, 220, 200);\n"
"background-color: rgb(229, 236, 236);\n"
"}")
        self.send_cmd.setObjectName("send_cmd")
        self.horizontalLayout_6.addWidget(self.send_cmd)
        self.cmd_text = QtWidgets.QLineEdit(self.widget_4)
        self.cmd_text.setEnabled(False)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.cmd_text.sizePolicy().hasHeightForWidth())
        self.cmd_text.setSizePolicy(sizePolicy)
        self.cmd_text.setMinimumSize(QtCore.QSize(770, 30))
        self.cmd_text.setObjectName("cmd_text")
        self.horizontalLayout_6.addWidget(self.cmd_text)
        self.verticalLayout.addLayout(self.horizontalLayout_6)
        self.verticalLayout.setStretch(1, 5)
        self.verticalLayout.setStretch(2, 30)
        self.verticalLayout.setStretch(3, 5)
        self.verticalLayout.setStretch(4, 30)
        self.horizontalLayout_8.addWidget(self.widget_4)
        self.horizontalLayout_8.setStretch(0, 70)
        self.horizontalLayout_8.setStretch(1, 870)
        self.verticalLayout_2.addWidget(self.widget_3)
        spacerItem13 = QtWidgets.QSpacerItem(20, 5, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Preferred)
        self.verticalLayout_2.addItem(spacerItem13)
        self.widget_6 = QtWidgets.QWidget(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(1)
        sizePolicy.setHeightForWidth(self.widget_6.sizePolicy().hasHeightForWidth())
        self.widget_6.setSizePolicy(sizePolicy)
        self.widget_6.setMinimumSize(QtCore.QSize(70, 0))
        self.widget_6.setStyleSheet("background-color: rgb(233, 185, 110);")
        self.widget_6.setObjectName("widget_6")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.widget_6)
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_10.setSpacing(0)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.image_label = QtWidgets.QLabel(self.widget_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.image_label.sizePolicy().hasHeightForWidth())
        self.image_label.setSizePolicy(sizePolicy)
        self.image_label.setMinimumSize(QtCore.QSize(70, 240))
        self.image_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.image_label.setAlignment(QtCore.Qt.AlignCenter)
        self.image_label.setObjectName("image_label")
        self.horizontalLayout_10.addWidget(self.image_label)
        self.widget_7 = QtWidgets.QWidget(self.widget_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(1)
        sizePolicy.setHeightForWidth(self.widget_7.sizePolicy().hasHeightForWidth())
        self.widget_7.setSizePolicy(sizePolicy)
        self.widget_7.setMinimumSize(QtCore.QSize(870, 150))
        self.widget_7.setStyleSheet("background-color: rgb(238, 238, 236);")
        self.widget_7.setObjectName("widget_7")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.widget_7)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setSpacing(10)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.image_display_label = QtWidgets.QLabel(self.widget_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(1)
        sizePolicy.setHeightForWidth(self.image_display_label.sizePolicy().hasHeightForWidth())
        self.image_display_label.setSizePolicy(sizePolicy)
        self.image_display_label.setMinimumSize(QtCore.QSize(850, 150))
        self.image_display_label.setStyleSheet("background-color: white; border: 1px solid #ddd;")
        self.image_display_label.setScaledContents(False)
        self.image_display_label.setAlignment(QtCore.Qt.AlignCenter)
        self.image_display_label.setObjectName("image_display_label")
        self.horizontalLayout_11.addWidget(self.image_display_label)
        self.horizontalLayout_10.addWidget(self.widget_7)
        self.horizontalLayout_10.setStretch(0, 70)
        self.horizontalLayout_10.setStretch(1, 870)
        self.verticalLayout_2.addWidget(self.widget_6)
        spacerItem14 = QtWidgets.QSpacerItem(20, 5, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Preferred)
        self.verticalLayout_2.addItem(spacerItem14)
        self.widget_8 = QtWidgets.QWidget(self.widget)
        self.widget_8.setStyleSheet("background-color: rgb(138, 226, 52);")
        self.widget_8.setObjectName("widget_8")
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout(self.widget_8)
        self.horizontalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_13.setSpacing(0)
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.label_logs = QtWidgets.QLabel(self.widget_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_logs.sizePolicy().hasHeightForWidth())
        self.label_logs.setSizePolicy(sizePolicy)
        self.label_logs.setMinimumSize(QtCore.QSize(70, 200))
        self.label_logs.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.label_logs.setAlignment(QtCore.Qt.AlignCenter)
        self.label_logs.setObjectName("label_logs")
        self.horizontalLayout_13.addWidget(self.label_logs)
        self.widget_9 = QtWidgets.QWidget(self.widget_8)
        self.widget_9.setMinimumSize(QtCore.QSize(870, 200))
        self.widget_9.setStyleSheet("background-color: rgb(238, 238, 236);")
        self.widget_9.setObjectName("widget_9")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.widget_9)
        self.horizontalLayout_12.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_12.setSpacing(0)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.received_textedit = QtWidgets.QPlainTextEdit(self.widget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.received_textedit.sizePolicy().hasHeightForWidth())
        self.received_textedit.setSizePolicy(sizePolicy)
        self.received_textedit.setMinimumSize(QtCore.QSize(870, 200))
        self.received_textedit.setObjectName("received_textedit")
        self.horizontalLayout_12.addWidget(self.received_textedit)
        self.horizontalLayout_13.addWidget(self.widget_9)
        self.horizontalLayout_13.setStretch(0, 70)
        self.horizontalLayout_13.setStretch(1, 870)
        self.verticalLayout_2.addWidget(self.widget_8)
        spacerItem15 = QtWidgets.QSpacerItem(20, 30, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Preferred)
        self.verticalLayout_2.addItem(spacerItem15)
        self.verticalLayout_2.setStretch(0, 10)
        self.verticalLayout_2.setStretch(1, 30)
        self.verticalLayout_2.setStretch(2, 5)
        self.verticalLayout_2.setStretch(3, 100)
        self.verticalLayout_2.setStretch(4, 5)
        self.verticalLayout_2.setStretch(5, 400)
        self.verticalLayout_2.setStretch(6, 5)
        self.verticalLayout_2.setStretch(7, 200)
        self.verticalLayout_2.setStretch(8, 30)
        self.horizontalLayout_14.addWidget(self.widget)
        spacerItem16 = QtWidgets.QSpacerItem(10, 20, QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_14.addItem(spacerItem16)
        self.horizontalLayout_14.setStretch(0, 10)
        self.horizontalLayout_14.setStretch(1, 940)
        self.horizontalLayout_14.setStretch(2, 10)
        MainWindow.setCentralWidget(self.container)
        self.menubar = QtWidgets.QMenuBar(MainWindow)
        self.menubar.setGeometry(QtCore.QRect(0, 0, 960, 24))
        self.menubar.setObjectName("menubar")
        self.menuMainFunction = QtWidgets.QMenu(self.menubar)
        self.menuMainFunction.setObjectName("menuMainFunction")
        self.menuDebug = QtWidgets.QMenu(self.menubar)
        self.menuDebug.setObjectName("menuDebug")
        self.menuHelp = QtWidgets.QMenu(self.menubar)
        self.menuHelp.setObjectName("menuHelp")
        self.menuConfig = QtWidgets.QMenu(self.menubar)
        self.menuConfig.setObjectName("menuConfig")
        self.menuRecognizeSafetyLevel = QtWidgets.QMenu(self.menuConfig)
        self.menuRecognizeSafetyLevel.setEnabled(False)
        self.menuRecognizeSafetyLevel.setObjectName("menuRecognizeSafetyLevel")
        self.menuSendFile = QtWidgets.QMenu(self.menubar)
        self.menuSendFile.setTitle("")
        self.menuSendFile.setObjectName("menuSendFile")
        self.menuOTA = QtWidgets.QMenu(self.menubar)
        self.menuOTA.setObjectName("menuOTA")
        MainWindow.setMenuBar(self.menubar)
        self.statusBar = QtWidgets.QStatusBar(MainWindow)
        self.statusBar.setObjectName("statusBar")
        MainWindow.setStatusBar(self.statusBar)
        self.action_connect = QtWidgets.QAction(MainWindow)
        font = QtGui.QFont()
        self.action_connect.setFont(font)
        self.action_connect.setObjectName("action_connect")
        self.action_disconnect = QtWidgets.QAction(MainWindow)
        self.action_disconnect.setEnabled(False)
        self.action_disconnect.setVisible(False)
        self.action_disconnect.setObjectName("action_disconnect")
        self.action_register = QtWidgets.QAction(MainWindow)
        self.action_register.setEnabled(False)
        self.action_register.setObjectName("action_register")
        self.action_recognize = QtWidgets.QAction(MainWindow)
        self.action_recognize.setEnabled(False)
        self.action_recognize.setObjectName("action_recognize")
        self.action_delete_alluser = QtWidgets.QAction(MainWindow)
        self.action_delete_alluser.setEnabled(False)
        self.action_delete_alluser.setObjectName("action_delete_alluser")
        self.action_get_user_feature = QtWidgets.QAction(MainWindow)
        self.action_get_user_feature.setEnabled(False)
        self.action_get_user_feature.setObjectName("action_get_user_feature")
        self.action_snap = QtWidgets.QAction(MainWindow)
        self.action_snap.setCheckable(True)
        self.action_snap.setEnabled(False)
        self.action_snap.setObjectName("action_snap")
        self.action_readimage = QtWidgets.QAction(MainWindow)
        self.action_readimage.setCheckable(True)
        self.action_readimage.setEnabled(False)
        self.action_readimage.setObjectName("action_readimage")
        self.action_about = QtWidgets.QAction(MainWindow)
        self.action_about.setObjectName("action_about")
        self.action_time_statistic = QtWidgets.QAction(MainWindow)
        self.action_time_statistic.setCheckable(False)
        self.action_time_statistic.setEnabled(False)
        self.action_time_statistic.setVisible(False)
        self.action_time_statistic.setObjectName("action_time_statistic")
        self.action_Open_OTA_Window = QtWidgets.QAction(MainWindow)
        self.action_Open_OTA_Window.setEnabled(False)
        self.action_Open_OTA_Window.setObjectName("action_Open_OTA_Window")
        self.action_Send_File = QtWidgets.QAction(MainWindow)
        self.action_Send_File.setEnabled(False)
        self.action_Send_File.setObjectName("action_Send_File")
        self.action_register_from_image = QtWidgets.QAction(MainWindow)
        self.action_register_from_image.setEnabled(False)
        self.action_register_from_image.setObjectName("action_register_from_image")
        self.action_recognize_lowest = QtWidgets.QAction(MainWindow)
        self.action_recognize_lowest.setCheckable(True)
        self.action_recognize_lowest.setEnabled(False)
        self.action_recognize_lowest.setShortcutVisibleInContextMenu(False)
        self.action_recognize_lowest.setObjectName("action_recognize_lowest")
        self.action_recognize_low = QtWidgets.QAction(MainWindow)
        self.action_recognize_low.setCheckable(True)
        self.action_recognize_low.setObjectName("action_recognize_low")
        self.action_recognize_normal = QtWidgets.QAction(MainWindow)
        self.action_recognize_normal.setCheckable(True)
        self.action_recognize_normal.setChecked(True)
        self.action_recognize_normal.setObjectName("action_recognize_normal")
        self.action_recognize_high = QtWidgets.QAction(MainWindow)
        self.action_recognize_high.setCheckable(True)
        self.action_recognize_high.setObjectName("action_recognize_high")
        self.action_recognize_highest = QtWidgets.QAction(MainWindow)
        self.action_recognize_highest.setCheckable(True)
        self.action_recognize_highest.setObjectName("action_recognize_highest")
        self.action_liveness_lowest = QtWidgets.QAction(MainWindow)
        self.action_liveness_lowest.setCheckable(True)
        self.action_liveness_lowest.setObjectName("action_liveness_lowest")
        self.action_liveness_low = QtWidgets.QAction(MainWindow)
        self.action_liveness_low.setCheckable(True)
        self.action_liveness_low.setObjectName("action_liveness_low")
        self.action_liveness_normal = QtWidgets.QAction(MainWindow)
        self.action_liveness_normal.setCheckable(True)
        self.action_liveness_normal.setChecked(True)
        self.action_liveness_normal.setObjectName("action_liveness_normal")
        self.action_liveness_high = QtWidgets.QAction(MainWindow)
        self.action_liveness_high.setCheckable(True)
        self.action_liveness_high.setObjectName("action_liveness_high")
        self.action_liveness_highest = QtWidgets.QAction(MainWindow)
        self.action_liveness_highest.setCheckable(True)
        self.action_liveness_highest.setObjectName("action_liveness_highest")
        self.action_stub = QtWidgets.QAction(MainWindow)
        self.action_stub.setEnabled(False)
        self.action_stub.setObjectName("action_stub")
        self.action_deletekey = QtWidgets.QAction(MainWindow)
        self.action_deletekey.setEnabled(False)
        self.action_deletekey.setObjectName("action_deletekey")
        self.action_enter_demo_mode = QtWidgets.QAction(MainWindow)
        self.action_enter_demo_mode.setObjectName("action_enter_demo_mode")
        self.action_exit_demo_mode = QtWidgets.QAction(MainWindow)
        self.action_exit_demo_mode.setObjectName("action_exit_demo_mode")
        self.action_delete_key = QtWidgets.QAction(MainWindow)
        self.action_delete_key.setEnabled(False)
        self.action_delete_key.setObjectName("action_delete_key")
        self.action_register_with_feature = QtWidgets.QAction(MainWindow)
        self.action_register_with_feature.setEnabled(False)
        self.action_register_with_feature.setObjectName("action_register_with_feature")
        self.action_register_single = QtWidgets.QAction(MainWindow)
        self.action_register_single.setEnabled(False)
        self.action_register_single.setObjectName("action_register_single")
        self.action_get_all_user_info = QtWidgets.QAction(MainWindow)
        self.action_get_all_user_info.setEnabled(False)
        self.action_get_all_user_info.setObjectName("action_get_all_user_info")
        self.action_Open_SendFile_Window = QtWidgets.QAction(MainWindow)
        self.action_Open_SendFile_Window.setEnabled(False)
        self.action_Open_SendFile_Window.setObjectName("action_Open_SendFile_Window")
        self.action_enter_debug_mode = QtWidgets.QAction(MainWindow)
        self.action_enter_debug_mode.setObjectName("action_enter_debug_mode")
        self.action_exit_debug_mode = QtWidgets.QAction(MainWindow)
        self.action_exit_debug_mode.setObjectName("action_exit_debug_mode")
        self.action_register_batch_features = QtWidgets.QAction(MainWindow)
        self.action_register_batch_features.setEnabled(False)
        self.action_register_batch_features.setObjectName("action_register_batch_features")
        self.action_register_batch_images = QtWidgets.QAction(MainWindow)
        self.action_register_batch_images.setEnabled(False)
        self.action_register_batch_images.setObjectName("action_register_batch_images")
        self.action_capture_when_success = QtWidgets.QAction(MainWindow)
        self.action_capture_when_success.setCheckable(True)
        self.action_capture_when_success.setChecked(True)
        self.action_capture_when_success.setObjectName("action_capture_when_success")
        self.action_capture_when_failed = QtWidgets.QAction(MainWindow)
        self.action_capture_when_failed.setCheckable(True)
        self.action_capture_when_failed.setChecked(True)
        self.action_capture_when_failed.setObjectName("action_capture_when_failed")
        self.action_autocapture = QtWidgets.QAction(MainWindow)
        self.action_autocapture.setCheckable(True)
        self.action_autocapture.setEnabled(True)
        self.action_autocapture.setVisible(True)
        self.action_autocapture.setObjectName("action_autocapture")
        self.action_change_capture_mode = QtWidgets.QAction(MainWindow)
        self.action_change_capture_mode.setEnabled(False)
        self.action_change_capture_mode.setObjectName("action_change_capture_mode")
        self.action_register_integrated = QtWidgets.QAction(MainWindow)
        self.action_register_integrated.setEnabled(False)
        self.action_register_integrated.setObjectName("action_register_integrated")
        self.action_get_logfiles = QtWidgets.QAction(MainWindow)
        self.action_get_logfiles.setEnabled(False)
        self.action_get_logfiles.setObjectName("action_get_logfiles")
        self.action_register_batched_RGB_images = QtWidgets.QAction(MainWindow)
        self.action_register_batched_RGB_images.setEnabled(False)
        self.action_register_batched_RGB_images.setObjectName("action_register_batched_RGB_images")
        self.action_register_RGB_image = QtWidgets.QAction(MainWindow)
        self.action_register_RGB_image.setEnabled(False)
        self.action_register_RGB_image.setObjectName("action_register_RGB_image")
        self.action_register_batched_IR_images = QtWidgets.QAction(MainWindow)
        self.action_register_batched_IR_images.setEnabled(False)
        self.action_register_batched_IR_images.setObjectName("action_register_batched_IR_images")
        self.action_register_IR_image = QtWidgets.QAction(MainWindow)
        self.action_register_IR_image.setEnabled(False)
        self.action_register_IR_image.setObjectName("action_register_IR_image")
        self.action_power_down = QtWidgets.QAction(MainWindow)
        self.action_power_down.setEnabled(False)
        self.action_power_down.setObjectName("action_power_down")
        self.action_modification_point = QtWidgets.QAction(MainWindow)
        self.action_modification_point.setVisible(False)
        self.action_modification_point.setObjectName("action_modification_point")
        self.action_snap_dual_raw_img = QtWidgets.QAction(MainWindow)
        self.action_snap_dual_raw_img.setEnabled(False)
        self.action_snap_dual_raw_img.setObjectName("action_snap_dual_raw_img")
        self.action_register_aligned_IR_image = QtWidgets.QAction(MainWindow)
        self.action_register_aligned_IR_image.setEnabled(False)
        self.action_register_aligned_IR_image.setObjectName("action_register_aligned_IR_image")
        self.action_register_batch_aligned_IR_image = QtWidgets.QAction(MainWindow)
        self.action_register_batch_aligned_IR_image.setEnabled(False)
        self.action_register_batch_aligned_IR_image.setObjectName("action_register_batch_aligned_IR_image")
        self.actionJumpFw1WithAcm = QtWidgets.QAction(MainWindow)
        self.actionJumpFw1WithAcm.setCheckable(False)
        self.actionJumpFw1WithAcm.setEnabled(False)
        self.actionJumpFw1WithAcm.setVisible(False)
        self.actionJumpFw1WithAcm.setObjectName("actionJumpFw1WithAcm")
        self.action_get_version = QtWidgets.QAction(MainWindow)
        self.action_get_version.setEnabled(False)
        self.action_get_version.setObjectName("action_get_version")
        self.action_get_log_size = QtWidgets.QAction(MainWindow)
        self.action_get_log_size.setEnabled(False)
        self.action_get_log_size.setObjectName("action_get_log_size")
        self.action_get_threshold = QtWidgets.QAction(MainWindow)
        self.action_get_threshold.setEnabled(False)
        self.action_get_threshold.setObjectName("action_get_threshold")
        self.action_label_recog = QtWidgets.QAction(MainWindow)
        self.action_label_recog.setEnabled(False)
        self.action_label_recog.setObjectName("action_label_recog")
        self.action_get_lib_model_version = QtWidgets.QAction(MainWindow)
        self.action_get_lib_model_version.setEnabled(False)
        self.action_get_lib_model_version.setObjectName("action_get_lib_model_version")
        self.action_set_threshold = QtWidgets.QAction(MainWindow)
        self.action_set_threshold.setEnabled(False)
        self.action_set_threshold.setObjectName("action_set_threshold")
        self.action_get_status = QtWidgets.QAction(MainWindow)
        self.action_get_status.setEnabled(False)
        self.action_get_status.setObjectName("action_get_status")
        self.action_reset = QtWidgets.QAction(MainWindow)
        self.action_reset.setEnabled(False)
        self.action_reset.setObjectName("action_reset")
        self.action_get_raw_image_size = QtWidgets.QAction(MainWindow)
        self.action_get_raw_image_size.setEnabled(False)
        self.action_get_raw_image_size.setObjectName("action_get_raw_image_size")
        self.action_upload_raw_image = QtWidgets.QAction(MainWindow)
        self.action_upload_raw_image.setEnabled(False)
        self.action_upload_raw_image.setObjectName("action_upload_raw_image")
        self.action_get_alg_image_size = QtWidgets.QAction(MainWindow)
        self.action_get_alg_image_size.setEnabled(False)
        self.action_get_alg_image_size.setObjectName("action_get_alg_image_size")
        self.action_upload_alg_image = QtWidgets.QAction(MainWindow)
        self.action_upload_alg_image.setEnabled(False)
        self.action_upload_alg_image.setObjectName("action_upload_alg_image")
        self.menuMainFunction.addAction(self.action_label_recog)
        self.menuMainFunction.addSeparator()
        self.menuMainFunction.addAction(self.action_get_version)
        self.menuMainFunction.addAction(self.action_get_lib_model_version)
        self.menuMainFunction.addAction(self.action_get_threshold)
        self.menuMainFunction.addAction(self.action_set_threshold)
        self.menuMainFunction.addAction(self.action_get_status)
        self.menuMainFunction.addSeparator()
        self.menuMainFunction.addAction(self.action_get_log_size)
        self.menuMainFunction.addAction(self.action_get_logfiles)
        self.menuMainFunction.addSeparator()
        self.menuMainFunction.addAction(self.action_get_raw_image_size)
        self.menuMainFunction.addAction(self.action_upload_raw_image)
        self.menuMainFunction.addAction(self.action_get_alg_image_size)
        self.menuMainFunction.addAction(self.action_upload_alg_image)
        self.menuMainFunction.addSeparator()
        self.menuMainFunction.addAction(self.action_reset)
        self.menuMainFunction.addAction(self.action_power_down)
        self.menuMainFunction.addSeparator()
        self.menuDebug.addAction(self.action_snap)
        self.menuDebug.addAction(self.action_readimage)
        self.menuHelp.addAction(self.action_about)
        self.menuHelp.addAction(self.action_modification_point)
        self.menuRecognizeSafetyLevel.addAction(self.action_recognize_lowest)
        self.menuRecognizeSafetyLevel.addAction(self.action_recognize_low)
        self.menuRecognizeSafetyLevel.addAction(self.action_recognize_normal)
        self.menuRecognizeSafetyLevel.addAction(self.action_recognize_high)
        self.menuRecognizeSafetyLevel.addAction(self.action_recognize_highest)
        self.menuConfig.addAction(self.action_autocapture)
        self.menuConfig.addAction(self.action_time_statistic)
        self.menuConfig.addSeparator()
        self.menuConfig.addAction(self.menuRecognizeSafetyLevel.menuAction())
        self.menuConfig.addSeparator()
        self.menuConfig.addAction(self.actionJumpFw1WithAcm)
        self.menuSendFile.addAction(self.action_Send_File)
        self.menuOTA.addAction(self.action_Open_OTA_Window)
        self.menuOTA.addAction(self.action_Open_SendFile_Window)
        self.menubar.addAction(self.menuMainFunction.menuAction())
        self.menubar.addAction(self.menuDebug.menuAction())
        self.menubar.addAction(self.menuConfig.menuAction())
        self.menubar.addAction(self.menuOTA.menuAction())
        self.menubar.addAction(self.menuHelp.menuAction())
        self.menubar.addAction(self.menuSendFile.menuAction())

        self.retranslateUi(MainWindow)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "MainWindow"))
        self.port_label.setText(_translate("MainWindow", "Serial Port"))
        self.port_combobox.setToolTip(_translate("MainWindow", "select com port to connect to device"))
        self.baudrate_combobox.setToolTip(_translate("MainWindow", "select baudrate for serial port"))
        self.connect_btn.setToolTip(_translate("MainWindow", "connect to device"))
        self.connect_btn.setText(_translate("MainWindow", "Connect"))
        self.disconnect_btn.setToolTip(_translate("MainWindow", "disconnect from device"))
        self.disconnect_btn.setText(_translate("MainWindow", "Disconnect"))
        self.functions_label.setText(_translate("MainWindow", "Functions"))
        self.cont_recognize_checkBox.setText(_translate("MainWindow", "cont_recognize"))
        self.recognize_btn.setToolTip(_translate("MainWindow", "start recognize"))
        self.recognize_btn.setText(_translate("MainWindow", "Start Recognize"))
        self.stop_recognize_btn.setToolTip(_translate("MainWindow", "stop recognize"))
        self.stop_recognize_btn.setText(_translate("MainWindow", "Stop Recognize"))
        self.snap_btn.setToolTip(_translate("MainWindow", "snap image from camera"))
        self.snap_btn.setText(_translate("MainWindow", "Snap"))
        self.read_raw_btn.setToolTip(_translate("MainWindow", "read raw image"))
        self.read_raw_btn.setText(_translate("MainWindow", "Read RAW"))
        self.read_alg_btn.setToolTip(_translate("MainWindow", "read algorithm image"))
        self.read_alg_btn.setText(_translate("MainWindow", "Read ALG"))
        self.send_cmd.setText(_translate("MainWindow", "SendCmd"))
        self.image_label.setText(_translate("MainWindow", "Image"))
        self.label_logs.setText(_translate("MainWindow", "Logs"))
        self.received_textedit.setToolTip(_translate("MainWindow", "show some log message"))
        self.menuMainFunction.setTitle(_translate("MainWindow", "&MainFunction"))
        self.menuDebug.setTitle(_translate("MainWindow", "&Debug"))
        self.menuHelp.setTitle(_translate("MainWindow", "&Help"))
        self.menuConfig.setTitle(_translate("MainWindow", "&Configuration"))
        self.menuRecognizeSafetyLevel.setTitle(_translate("MainWindow", "RecognizeSafetyLevel"))
        self.menuOTA.setTitle(_translate("MainWindow", "&OTA"))
        self.action_connect.setText(_translate("MainWindow", "&Connect"))
        self.action_connect.setToolTip(_translate("MainWindow", "connect to device"))
        self.action_disconnect.setText(_translate("MainWindow", "Disconnec&t"))
        self.action_disconnect.setToolTip(_translate("MainWindow", "disconnect from device"))
        self.action_register.setText(_translate("MainWindow", "&Register"))
        self.action_register.setToolTip(_translate("MainWindow", "register new user from giving image"))
        self.action_recognize.setText(_translate("MainWindow", "&Recognize"))
        self.action_recognize.setToolTip(_translate("MainWindow", "recognize current face in front of camera"))
        self.action_delete_alluser.setText(_translate("MainWindow", "&DeleteAllUser"))
        self.action_delete_alluser.setToolTip(_translate("MainWindow", "delete all registerd face from device"))
        self.action_get_user_feature.setText(_translate("MainWindow", "GetUserFeature"))
        self.action_get_user_feature.setToolTip(_translate("MainWindow", "get user feature"))
        self.action_snap.setText(_translate("MainWindow", "&Snap"))
        self.action_snap.setToolTip(_translate("MainWindow", "snap image from camera"))
        self.action_readimage.setText(_translate("MainWindow", "&ReadImage"))
        self.action_readimage.setToolTip(_translate("MainWindow", "read snaped image"))
        self.action_about.setText(_translate("MainWindow", "&About"))
        self.action_about.setToolTip(_translate("MainWindow", "about facelock app"))
        self.action_time_statistic.setText(_translate("MainWindow", "&TimeStatistic"))
        self.action_time_statistic.setToolTip(_translate("MainWindow", "time statistic for recognize process"))
        self.action_Open_OTA_Window.setText(_translate("MainWindow", "&Open OTA Window"))
        self.action_Open_OTA_Window.setToolTip(_translate("MainWindow", "open OTA window and start OTA"))
        self.action_Send_File.setText(_translate("MainWindow", "&Send File"))
        self.action_register_from_image.setText(_translate("MainWindow", "RegisterFrom&Image"))
        self.action_recognize_lowest.setText(_translate("MainWindow", "Lowest"))
        self.action_recognize_low.setText(_translate("MainWindow", "Low"))
        self.action_recognize_normal.setText(_translate("MainWindow", "Normal"))
        self.action_recognize_high.setText(_translate("MainWindow", "High"))
        self.action_recognize_highest.setText(_translate("MainWindow", "Highest"))
        self.action_liveness_lowest.setText(_translate("MainWindow", "Lowest"))
        self.action_liveness_low.setText(_translate("MainWindow", "Low"))
        self.action_liveness_normal.setText(_translate("MainWindow", "Normal"))
        self.action_liveness_high.setText(_translate("MainWindow", "High"))
        self.action_liveness_highest.setText(_translate("MainWindow", "Highest"))
        self.action_stub.setText(_translate("MainWindow", "ActionStub"))
        self.action_deletekey.setText(_translate("MainWindow", "DeleteKey"))
        self.action_enter_demo_mode.setText(_translate("MainWindow", "Enter"))
        self.action_exit_demo_mode.setText(_translate("MainWindow", "Exit"))
        self.action_delete_key.setText(_translate("MainWindow", "DeleteKey"))
        self.action_delete_key.setToolTip(_translate("MainWindow", "delete encryption key"))
        self.action_register_with_feature.setText(_translate("MainWindow", "RegisterWithFeature"))
        self.action_register_with_feature.setToolTip(_translate("MainWindow", "register face with feature file"))
        self.action_register_single.setText(_translate("MainWindow", "RegisterSingle"))
        self.action_register_single.setToolTip(_translate("MainWindow", "register with single position face"))
        self.action_get_all_user_info.setText(_translate("MainWindow", "GetAllUserInfo"))
        self.action_get_all_user_info.setToolTip(_translate("MainWindow", "get all user info"))
        self.action_Open_SendFile_Window.setText(_translate("MainWindow", "Open SendFile Window"))
        self.action_enter_debug_mode.setText(_translate("MainWindow", "enter_debug_mode"))
        self.action_exit_debug_mode.setText(_translate("MainWindow", "exit_debug_mode"))
        self.action_register_batch_features.setText(_translate("MainWindow", "RegisterBatchFeatures"))
        self.action_register_batch_images.setText(_translate("MainWindow", "RegisterBatchImages"))
        self.action_capture_when_success.setText(_translate("MainWindow", "CaptureWhenSuccess"))
        self.action_capture_when_failed.setText(_translate("MainWindow", "CaptureWhenFailed"))
        self.action_autocapture.setText(_translate("MainWindow", "Auto&Capture"))
        self.action_change_capture_mode.setText(_translate("MainWindow", "ChangeCaptureMode"))
        self.action_register_integrated.setText(_translate("MainWindow", "RegisterIntegrated"))
        self.action_get_logfiles.setText(_translate("MainWindow", "GetLogFiles"))
        self.action_register_batched_RGB_images.setText(_translate("MainWindow", "RegisterBatchedRGBImages"))
        self.action_register_RGB_image.setText(_translate("MainWindow", "RegisterRGBImage"))
        self.action_register_batched_IR_images.setText(_translate("MainWindow", "RegisterBatchedIRImages"))
        self.action_register_IR_image.setText(_translate("MainWindow", "RegisterIRImage"))
        self.action_power_down.setText(_translate("MainWindow", "PowerDown"))
        self.action_modification_point.setText(_translate("MainWindow", "版本修改点"))
        self.action_snap_dual_raw_img.setText(_translate("MainWindow", "双目原图抓拍"))
        self.action_register_aligned_IR_image.setText(_translate("MainWindow", "RegisterAlignedIRImage"))
        self.action_register_batch_aligned_IR_image.setText(_translate("MainWindow", "RegisterBatchAlignedIRImage"))
        self.actionJumpFw1WithAcm.setText(_translate("MainWindow", "JumpFw1WithAcm"))
        self.action_get_version.setText(_translate("MainWindow", "Get Version"))
        self.action_get_version.setToolTip(_translate("MainWindow", "get device version information"))
        self.action_get_log_size.setText(_translate("MainWindow", "Get Log Size"))
        self.action_get_log_size.setToolTip(_translate("MainWindow", "get log size from device"))
        self.action_get_threshold.setText(_translate("MainWindow", "Get Threshold"))
        self.action_get_threshold.setToolTip(_translate("MainWindow", "get threshold settings from device"))
        self.action_label_recog.setText(_translate("MainWindow", "Label Recognition"))
        self.action_label_recog.setToolTip(_translate("MainWindow", "start label recognition"))
        self.action_get_lib_model_version.setText(_translate("MainWindow", "Get Lib Model Version"))
        self.action_get_lib_model_version.setToolTip(_translate("MainWindow", "get algorithm library and model library version information"))
        self.action_set_threshold.setText(_translate("MainWindow", "Set Threshold"))
        self.action_set_threshold.setToolTip(_translate("MainWindow", "set recognition threshold level"))
        self.action_get_status.setText(_translate("MainWindow", "Get Status"))
        self.action_get_status.setToolTip(_translate("MainWindow", "get current module status immediately"))
        self.action_reset.setText(_translate("MainWindow", "Reset Module"))
        self.action_reset.setToolTip(_translate("MainWindow", "stop current processing, module enters standby"))
        self.action_get_raw_image_size.setText(_translate("MainWindow", "Get Raw Image Size"))
        self.action_get_raw_image_size.setToolTip(_translate("MainWindow", "get the size of raw image to be uploaded"))
        self.action_upload_raw_image.setText(_translate("MainWindow", "Upload Raw Image"))
        self.action_upload_raw_image.setToolTip(_translate("MainWindow", "upload raw image to host"))
        self.action_get_alg_image_size.setText(_translate("MainWindow", "Get Algorithm Image Size"))
        self.action_get_alg_image_size.setToolTip(_translate("MainWindow", "get the size of algorithm image to be uploaded"))
        self.action_upload_alg_image.setText(_translate("MainWindow", "Upload Algorithm Image"))
        self.action_upload_alg_image.setToolTip(_translate("MainWindow", "upload algorithm image to host"))


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    MainWindow = QtWidgets.QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(MainWindow)
    MainWindow.show()
    sys.exit(app.exec_())
