# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'OtaWindowUI.ui'
#
# Created by: PyQt5 UI code generator 5.15.6
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_OTAWindowUI(object):
    def setupUi(self, OTAWindowUI):
        OTAWindowUI.setObjectName("OTAWindowUI")
        OTAWindowUI.resize(373, 333)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(OTAWindowUI.sizePolicy().hasHeightForWidth())
        OTAWindowUI.setSizePolicy(sizePolicy)
        self.centralwidget = QtWidgets.QWidget(OTAWindowUI)
        self.centralwidget.setObjectName("centralwidget")
        self.otastatus_textEdit = QtWidgets.QTextEdit(self.centralwidget)
        self.otastatus_textEdit.setGeometry(QtCore.QRect(20, 90, 331, 141))
        self.otastatus_textEdit.setObjectName("otastatus_textEdit")
        self.selectfile_btn = QtWidgets.QPushButton(self.centralwidget)
        self.selectfile_btn.setGeometry(QtCore.QRect(20, 10, 99, 31))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.selectfile_btn.sizePolicy().hasHeightForWidth())
        self.selectfile_btn.setSizePolicy(sizePolicy)
        self.selectfile_btn.setObjectName("selectfile_btn")
        self.selectfile_label = QtWidgets.QLabel(self.centralwidget)
        self.selectfile_label.setGeometry(QtCore.QRect(300, 30, 61, 21))
        self.selectfile_label.setText("")
        self.selectfile_label.setObjectName("selectfile_label")
        self.ota_progressBar = QtWidgets.QProgressBar(self.centralwidget)
        self.ota_progressBar.setGeometry(QtCore.QRect(120, 240, 231, 31))
        self.ota_progressBar.setProperty("value", 0)
        self.ota_progressBar.setObjectName("ota_progressBar")
        self.startOTA_btn = QtWidgets.QPushButton(self.centralwidget)
        self.startOTA_btn.setGeometry(QtCore.QRect(20, 240, 91, 31))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.startOTA_btn.sizePolicy().hasHeightForWidth())
        self.startOTA_btn.setSizePolicy(sizePolicy)
        self.startOTA_btn.setIconSize(QtCore.QSize(16, 16))
        self.startOTA_btn.setObjectName("startOTA_btn")
        self.txt_radio_Button = QtWidgets.QRadioButton(self.centralwidget)
        self.txt_radio_Button.setGeometry(QtCore.QRect(280, 10, 112, 23))
        self.txt_radio_Button.setChecked(True)
        self.txt_radio_Button.setObjectName("txt_radio_Button")
        self.ota_radio_Button = QtWidgets.QRadioButton(self.centralwidget)
        self.ota_radio_Button.setGeometry(QtCore.QRect(280, 30, 112, 23))
        self.ota_radio_Button.setObjectName("ota_radio_Button")
        self.json_radio_Button = QtWidgets.QRadioButton(self.centralwidget)
        self.json_radio_Button.setGeometry(QtCore.QRect(280, 50, 112, 23))
        self.json_radio_Button.setObjectName("json_radio_Button")
        self.bps_cbx = QtWidgets.QComboBox(self.centralwidget)
        self.bps_cbx.setGeometry(QtCore.QRect(170, 10, 91, 25))
        self.bps_cbx.setObjectName("bps_cbx")
        self.label = QtWidgets.QLabel(self.centralwidget)
        self.label.setGeometry(QtCore.QRect(130, 10, 41, 17))
        self.label.setObjectName("label")
        self.label_2 = QtWidgets.QLabel(self.centralwidget)
        self.label_2.setGeometry(QtCore.QRect(130, 50, 41, 17))
        self.label_2.setObjectName("label_2")
        self.pkt_cbx = QtWidgets.QComboBox(self.centralwidget)
        self.pkt_cbx.setGeometry(QtCore.QRect(170, 50, 91, 25))
        self.pkt_cbx.setObjectName("pkt_cbx")
        OTAWindowUI.setCentralWidget(self.centralwidget)
        self.menubar = QtWidgets.QMenuBar(OTAWindowUI)
        self.menubar.setGeometry(QtCore.QRect(0, 0, 373, 28))
        self.menubar.setObjectName("menubar")
        OTAWindowUI.setMenuBar(self.menubar)
        self.statusbar = QtWidgets.QStatusBar(OTAWindowUI)
        self.statusbar.setObjectName("statusbar")
        OTAWindowUI.setStatusBar(self.statusbar)

        self.retranslateUi(OTAWindowUI)
        QtCore.QMetaObject.connectSlotsByName(OTAWindowUI)

    def retranslateUi(self, OTAWindowUI):
        _translate = QtCore.QCoreApplication.translate
        OTAWindowUI.setWindowTitle(_translate("OTAWindowUI", "OTA test tool"))
        self.selectfile_btn.setText(_translate("OTAWindowUI", "selectfile"))
        self.startOTA_btn.setText(_translate("OTAWindowUI", "start"))
        self.txt_radio_Button.setText(_translate("OTAWindowUI", ".txt"))
        self.ota_radio_Button.setText(_translate("OTAWindowUI", ".ota"))
        self.json_radio_Button.setText(_translate("OTAWindowUI", ".json"))
        self.label.setText(_translate("OTAWindowUI", "bps:"))
        self.label_2.setText(_translate("OTAWindowUI", "pkt:"))


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    OTAWindowUI = QtWidgets.QMainWindow()
    ui = Ui_OTAWindowUI()
    ui.setupUi(OTAWindowUI)
    OTAWindowUI.show()
    sys.exit(app.exec_())
