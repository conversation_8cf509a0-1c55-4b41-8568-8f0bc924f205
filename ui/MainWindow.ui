<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <author>joeycao</author>
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>960</width>
    <height>653</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>960</width>
    <height>653</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(195, 229, 229);</string>
  </property>
  <widget class="QWidget" name="container">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="minimumSize">
    <size>
     <width>960</width>
     <height>625</height>
    </size>
   </property>
   <property name="maximumSize">
    <size>
     <width>16777215</width>
     <height>16777215</height>
    </size>
   </property>
   <property name="styleSheet">
    <string notr="true">background-color: rgb(195, 229, 229);</string>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_14" stretch="10,940,10">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <spacer name="horizontalSpacer_14">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <property name="sizeType">
       <enum>QSizePolicy::Preferred</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>10</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QWidget" name="widget" native="true">
      <property name="minimumSize">
       <size>
        <width>940</width>
        <height>625</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2" stretch="10,30,5,100,5,400,5,200,30">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <spacer name="verticalSpacer_3">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Preferred</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>10</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QWidget" name="widget_2" native="true">
         <property name="minimumSize">
          <size>
           <width>940</width>
           <height>30</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(211, 190, 230);</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="70,870">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QLabel" name="port_label">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>70</width>
              <height>30</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>70</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>Serial Port</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="widget_5" native="true">
            <property name="minimumSize">
             <size>
              <width>870</width>
              <height>30</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(238, 238, 236);
</string>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="665,5,200">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="270,5,270">
               <property name="spacing">
                <number>0</number>
               </property>
               <property name="sizeConstraint">
                <enum>QLayout::SetDefaultConstraint</enum>
               </property>
               <item>
                <widget class="QComboBox" name="port_combobox">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>330</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="toolTip">
                  <string>select com port to connect to device</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_3">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeType">
                  <enum>QSizePolicy::Preferred</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>5</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item>
                <widget class="QComboBox" name="baudrate_combobox">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>330</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="toolTip">
                  <string>select baudrate for serial port</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="horizontalSpacer_4">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Preferred</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>5</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="0,0">
               <property name="spacing">
                <number>0</number>
               </property>
               <property name="sizeConstraint">
                <enum>QLayout::SetDefaultConstraint</enum>
               </property>
               <item>
                <widget class="QPushButton" name="connect_btn">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>100</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="toolTip">
                  <string>connect to device</string>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#connect_btn{
color: rgb(0, 0, 0);
background-color: rgb(247, 255, 255);
}
#connect_btn:hover{
color: rgb(0, 120, 235);
background-color: rgb(238, 245, 245);
}
#connect_btn:pressed{
color: rgb(20, 220, 200);
background-color: rgb(229, 236, 236);
}</string>
                 </property>
                 <property name="text">
                  <string>Connect</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="disconnect_btn">
                 <property name="enabled">
                  <bool>true</bool>
                 </property>
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>100</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="toolTip">
                  <string>disconnect from device</string>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#disconnect_btn{
color: rgb(0, 0, 0);
background-color: rgb(247, 255, 255);
}
#disconnect_btn:hover{
color: rgb(0, 120, 235);
background-color: rgb(238, 245, 245);
}
#disconnect_btn:pressed{
color: rgb(20, 220, 200);
background-color: rgb(229, 236, 236);
}</string>
                 </property>
                 <property name="text">
                  <string>Disconnect</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_4">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Preferred</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>5</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QWidget" name="widget_3" native="true">
         <property name="minimumSize">
          <size>
           <width>940</width>
           <height>100</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(241, 246, 207);</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="70,870">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QLabel" name="functions_label">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>70</width>
              <height>100</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>Functions</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="widget_4" native="true">
            <property name="minimumSize">
             <size>
              <width>870</width>
              <height>100</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(238, 238, 236);</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout" stretch="0,5,30,5,30">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout">
               <property name="spacing">
                <number>0</number>
               </property>
               <property name="sizeConstraint">
                <enum>QLayout::SetDefaultConstraint</enum>
               </property>
              </layout>
             </item>
             <item>
              <spacer name="verticalSpacer">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Preferred</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>5</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="100,50,5,100,5,100,5,100,5,100,5,0,0">
               <property name="spacing">
                <number>0</number>
               </property>
               <property name="sizeConstraint">
                <enum>QLayout::SetDefaultConstraint</enum>
               </property>
               <property name="leftMargin">
                <number>0</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="QCheckBox" name="cont_recognize_checkBox">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>100</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#cont_recognize_checkBox{
background-color: rgb(238, 238, 236);
}
#cont_recognize_checkBox:hover{
color: rgb(0, 120, 235);
background-color: rgb(238, 245, 245);
}
#cont_recognize_checkBox:pressed{
color: rgb(20, 220, 200);
background-color: rgb(229, 236, 236);
}</string>
                 </property>
                 <property name="text">
                  <string>cont_recognize</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="recognize_times_lineedit">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>50</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_10">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeType">
                  <enum>QSizePolicy::Preferred</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>5</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item>
                <widget class="QPushButton" name="recognize_btn">
                 <property name="enabled">
                  <bool>true</bool>
                 </property>
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>100</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="focusPolicy">
                  <enum>Qt::StrongFocus</enum>
                 </property>
                 <property name="toolTip">
                  <string>start recognize</string>
                 </property>
                 <property name="text">
                  <string>Start Recognize</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_11">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeType">
                  <enum>QSizePolicy::Preferred</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>5</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item>
                <widget class="QPushButton" name="stop_recognize_btn">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>100</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="toolTip">
                  <string>stop recognize</string>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#stop_recognize_btn{
background-color: rgb(238, 238, 236);
}
#stop_recognize_btn:hover{
color: rgb(0, 120, 235);
background-color: rgb(238, 245, 245);
}
#stop_recognize_btn:pressed{
color: rgb(20, 220, 200);
background-color: rgb(229, 236, 236);
}</string>
                 </property>
                 <property name="text">
                  <string>Stop Recognize</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_12">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeType">
                  <enum>QSizePolicy::Preferred</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>5</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item>
                <widget class="QPushButton" name="snap_btn">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>100</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="toolTip">
                  <string>snap image from camera</string>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#snap_btn{
background-color: rgb(238, 238, 236);
}
#snap_btn:hover{
color: rgb(0, 120, 235);
background-color: rgb(238, 245, 245);
}
#snap_btn:pressed{
color: rgb(20, 220, 200);
background-color: rgb(229, 236, 236);
}</string>
                 </property>
                 <property name="text">
                  <string>Snap</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_13">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeType">
                  <enum>QSizePolicy::Preferred</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>5</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item>
                <widget class="QPushButton" name="read_raw_btn">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>100</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="toolTip">
                  <string>read raw image</string>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#read_raw_btn{
background-color: rgb(238, 238, 236);
}
#read_raw_btn:hover{
color: rgb(0, 120, 235);
background-color: rgb(238, 245, 245);
}
#read_raw_btn:pressed{
color: rgb(20, 220, 200);
background-color: rgb(229, 236, 236);
}</string>
                 </property>
                 <property name="text">
                  <string>Read RAW</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_14">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeType">
                  <enum>QSizePolicy::Preferred</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>5</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item>
                <widget class="QPushButton" name="read_alg_btn">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>100</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="toolTip">
                  <string>read algorithm image</string>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#read_alg_btn{
background-color: rgb(238, 238, 236);
}
#read_alg_btn:hover{
color: rgb(0, 120, 235);
background-color: rgb(238, 245, 245);
}
#read_alg_btn:pressed{
color: rgb(20, 220, 200);
background-color: rgb(229, 236, 236);
}</string>
                 </property>
                 <property name="text">
                  <string>Read ALG</string>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>10</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="verticalSpacer_2">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Preferred</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>5</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_6">
               <item>
                <widget class="QPushButton" name="send_cmd">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>100</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#send_cmd{
background-color: rgb(238, 238, 236);
}
#send_cmd:hover{
color: rgb(0, 120, 235);
background-color: rgb(238, 245, 245);
}
#send_cmd:pressed{
color: rgb(20, 220, 200);
background-color: rgb(229, 236, 236);
}</string>
                 </property>
                 <property name="text">
                  <string>SendCmd</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="cmd_text">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>770</width>
                   <height>30</height>
                  </size>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_5">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Preferred</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>5</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QWidget" name="widget_6" native="true">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>1</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>70</width>
           <height>0</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(233, 185, 110);</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_10" stretch="70,870">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QLabel" name="image_label">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>70</width>
              <height>240</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>Image</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="widget_7" native="true">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
              <horstretch>0</horstretch>
              <verstretch>1</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>870</width>
              <height>150</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(238, 238, 236);</string>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_11">
             <property name="spacing">
              <number>10</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="image_display_label">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>1</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>850</width>
                 <height>150</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">background-color: white; border: 1px solid #ddd;</string>
               </property>
               <property name="scaledContents">
                <bool>false</bool>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_6">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Preferred</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>5</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QWidget" name="widget_8" native="true">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(138, 226, 52);</string>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_13" stretch="70,870">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QLabel" name="label_logs">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>70</width>
              <height>200</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="text">
             <string>Logs</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="widget_9" native="true">
            <property name="minimumSize">
             <size>
              <width>870</width>
              <height>200</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-color: rgb(238, 238, 236);</string>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_12">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QPlainTextEdit" name="received_textedit">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>870</width>
                 <height>200</height>
                </size>
               </property>
               <property name="toolTip">
                <string>show some log message</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_7">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Preferred</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>30</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <spacer name="horizontalSpacer_15">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <property name="sizeType">
       <enum>QSizePolicy::Preferred</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>10</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>960</width>
     <height>24</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuMainFunction">
    <property name="title">
     <string>&amp;MainFunction</string>
    </property>
    <addaction name="action_label_recog"/>
    <addaction name="separator"/>
    <addaction name="action_get_version"/>
    <addaction name="action_get_lib_model_version"/>
    <addaction name="action_get_threshold"/>
    <addaction name="action_set_threshold"/>
    <addaction name="action_get_status"/>
    <addaction name="separator"/>
    <addaction name="action_get_log_size"/>
    <addaction name="action_get_logfiles"/>
    <addaction name="separator"/>
    <addaction name="action_get_raw_image_size"/>
    <addaction name="action_upload_raw_image"/>
    <addaction name="action_get_alg_image_size"/>
    <addaction name="action_upload_alg_image"/>
    <addaction name="separator"/>
    <addaction name="action_reset"/>
    <addaction name="action_power_down"/>
    <addaction name="separator"/>
   </widget>
   <widget class="QMenu" name="menuDebug">
    <property name="title">
     <string>&amp;Debug</string>
    </property>
    <addaction name="action_snap"/>
    <addaction name="action_readimage"/>
   </widget>
   <widget class="QMenu" name="menuHelp">
    <property name="title">
     <string>&amp;Help</string>
    </property>
    <addaction name="action_about"/>
    <addaction name="action_modification_point"/>
   </widget>
   <widget class="QMenu" name="menuConfig">
    <property name="title">
     <string>&amp;Configuration</string>
    </property>
    <widget class="QMenu" name="menuRecognizeSafetyLevel">
     <property name="enabled">
      <bool>false</bool>
     </property>
     <property name="title">
      <string>RecognizeSafetyLevel</string>
     </property>
     <addaction name="action_recognize_lowest"/>
     <addaction name="action_recognize_low"/>
     <addaction name="action_recognize_normal"/>
     <addaction name="action_recognize_high"/>
     <addaction name="action_recognize_highest"/>
    </widget>
    <addaction name="action_autocapture"/>
    <addaction name="action_time_statistic"/>
    <addaction name="separator"/>
    <addaction name="menuRecognizeSafetyLevel"/>
    <addaction name="separator"/>
    <addaction name="actionJumpFw1WithAcm"/>
   </widget>
   <widget class="QMenu" name="menuSendFile">
    <property name="title">
     <string/>
    </property>
    <addaction name="action_Send_File"/>
   </widget>
   <widget class="QMenu" name="menuOTA">
    <property name="title">
     <string>&amp;OTA</string>
    </property>
    <addaction name="action_Open_OTA_Window"/>
    <addaction name="action_Open_SendFile_Window"/>
   </widget>
   <addaction name="menuMainFunction"/>
   <addaction name="menuDebug"/>
   <addaction name="menuConfig"/>
   <addaction name="menuOTA"/>
   <addaction name="menuHelp"/>
   <addaction name="menuSendFile"/>
  </widget>
  <widget class="QStatusBar" name="statusBar"/>
  <action name="action_connect">
   <property name="text">
    <string>&amp;Connect</string>
   </property>
   <property name="toolTip">
    <string>connect to device</string>
   </property>
   <property name="font">
    <font/>
   </property>
  </action>
  <action name="action_disconnect">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Disconnec&amp;t</string>
   </property>
   <property name="toolTip">
    <string>disconnect from device</string>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
  </action>
  <action name="action_register">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>&amp;Register</string>
   </property>
   <property name="toolTip">
    <string>register new user from giving image</string>
   </property>
  </action>
  <action name="action_recognize">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>&amp;Recognize</string>
   </property>
   <property name="toolTip">
    <string>recognize current face in front of camera</string>
   </property>
  </action>
  <action name="action_delete_alluser">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>&amp;DeleteAllUser</string>
   </property>
   <property name="toolTip">
    <string>delete all registerd face from device</string>
   </property>
  </action>
  <action name="action_get_user_feature">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>GetUserFeature</string>
   </property>
   <property name="toolTip">
    <string>get user feature</string>
   </property>
  </action>
  <action name="action_snap">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>&amp;Snap</string>
   </property>
   <property name="toolTip">
    <string>snap image from camera</string>
   </property>
  </action>
  <action name="action_readimage">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>&amp;ReadImage</string>
   </property>
   <property name="toolTip">
    <string>read snaped image</string>
   </property>
  </action>
  <action name="action_about">
   <property name="text">
    <string>&amp;About</string>
   </property>
   <property name="toolTip">
    <string>about facelock app</string>
   </property>
  </action>
  <action name="action_time_statistic">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>&amp;TimeStatistic</string>
   </property>
   <property name="toolTip">
    <string>time statistic for recognize process</string>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
  </action>
  <action name="action_Open_OTA_Window">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>&amp;Open OTA Window</string>
   </property>
   <property name="toolTip">
    <string>open OTA window and start OTA</string>
   </property>
  </action>
  <action name="action_Send_File">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>&amp;Send File</string>
   </property>
  </action>
  <action name="action_register_from_image">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>RegisterFrom&amp;Image</string>
   </property>
  </action>
  <action name="action_recognize_lowest">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Lowest</string>
   </property>
   <property name="shortcutVisibleInContextMenu">
    <bool>false</bool>
   </property>
  </action>
  <action name="action_recognize_low">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Low</string>
   </property>
  </action>
  <action name="action_recognize_normal">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Normal</string>
   </property>
  </action>
  <action name="action_recognize_high">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>High</string>
   </property>
  </action>
  <action name="action_recognize_highest">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Highest</string>
   </property>
  </action>
  <action name="action_liveness_lowest">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Lowest</string>
   </property>
  </action>
  <action name="action_liveness_low">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Low</string>
   </property>
  </action>
  <action name="action_liveness_normal">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Normal</string>
   </property>
  </action>
  <action name="action_liveness_high">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>High</string>
   </property>
  </action>
  <action name="action_liveness_highest">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Highest</string>
   </property>
  </action>
  <action name="action_stub">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>ActionStub</string>
   </property>
  </action>
  <action name="action_deletekey">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>DeleteKey</string>
   </property>
  </action>
  <action name="action_enter_demo_mode">
   <property name="text">
    <string>Enter</string>
   </property>
  </action>
  <action name="action_exit_demo_mode">
   <property name="text">
    <string>Exit</string>
   </property>
  </action>
  <action name="action_delete_key">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>DeleteKey</string>
   </property>
   <property name="toolTip">
    <string>delete encryption key</string>
   </property>
  </action>
  <action name="action_register_with_feature">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>RegisterWithFeature</string>
   </property>
   <property name="toolTip">
    <string>register face with feature file</string>
   </property>
  </action>
  <action name="action_register_single">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>RegisterSingle</string>
   </property>
   <property name="toolTip">
    <string>register with single position face</string>
   </property>
  </action>
  <action name="action_get_all_user_info">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>GetAllUserInfo</string>
   </property>
   <property name="toolTip">
    <string>get all user info</string>
   </property>
  </action>
  <action name="action_Open_SendFile_Window">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Open SendFile Window</string>
   </property>
  </action>
  <action name="action_enter_debug_mode">
   <property name="text">
    <string>enter_debug_mode</string>
   </property>
  </action>
  <action name="action_exit_debug_mode">
   <property name="text">
    <string>exit_debug_mode</string>
   </property>
  </action>
  <action name="action_register_batch_features">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>RegisterBatchFeatures</string>
   </property>
  </action>
  <action name="action_register_batch_images">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>RegisterBatchImages</string>
   </property>
  </action>
  <action name="action_capture_when_success">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>CaptureWhenSuccess</string>
   </property>
  </action>
  <action name="action_capture_when_failed">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>CaptureWhenFailed</string>
   </property>
  </action>
  <action name="action_autocapture">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Auto&amp;Capture</string>
   </property>
   <property name="visible">
    <bool>true</bool>
   </property>
  </action>
  <action name="action_change_capture_mode">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>ChangeCaptureMode</string>
   </property>
  </action>
  <action name="action_register_integrated">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>RegisterIntegrated</string>
   </property>
  </action>
  <action name="action_get_logfiles">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>GetLogFiles</string>
   </property>
  </action>
  <action name="action_register_batched_RGB_images">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>RegisterBatchedRGBImages</string>
   </property>
  </action>
  <action name="action_register_RGB_image">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>RegisterRGBImage</string>
   </property>
  </action>
  <action name="action_register_batched_IR_images">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>RegisterBatchedIRImages</string>
   </property>
  </action>
  <action name="action_register_IR_image">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>RegisterIRImage</string>
   </property>
  </action>
  <action name="action_power_down">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>PowerDown</string>
   </property>
  </action>
  <action name="action_modification_point">
   <property name="text">
    <string>版本修改点</string>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
  </action>
  <action name="action_snap_dual_raw_img">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>双目原图抓拍</string>
   </property>
  </action>
  <action name="action_register_aligned_IR_image">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>RegisterAlignedIRImage</string>
   </property>
  </action>
  <action name="action_register_batch_aligned_IR_image">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>RegisterBatchAlignedIRImage</string>
   </property>
  </action>
  <action name="actionJumpFw1WithAcm">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>JumpFw1WithAcm</string>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
  </action>
  <action name="action_get_version">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Get Version</string>
   </property>
   <property name="toolTip">
    <string>get device version information</string>
   </property>
  </action>
  <action name="action_get_log_size">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Get Log Size</string>
   </property>
   <property name="toolTip">
    <string>get log size from device</string>
   </property>
  </action>
  <action name="action_get_threshold">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Get Threshold</string>
   </property>
   <property name="toolTip">
    <string>get threshold settings from device</string>
   </property>
  </action>
  <action name="action_label_recog">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Label Recognition</string>
   </property>
   <property name="toolTip">
    <string>start label recognition</string>
   </property>
  </action>
  <action name="action_get_lib_model_version">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Get Lib Model Version</string>
   </property>
   <property name="toolTip">
    <string>get algorithm library and model library version information</string>
   </property>
  </action>
  <action name="action_set_threshold">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Set Threshold</string>
   </property>
   <property name="toolTip">
    <string>set recognition threshold level</string>
   </property>
  </action>
  <action name="action_get_status">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Get Status</string>
   </property>
   <property name="toolTip">
    <string>get current module status immediately</string>
   </property>
  </action>
  <action name="action_reset">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Reset Module</string>
   </property>
   <property name="toolTip">
    <string>stop current processing, module enters standby</string>
   </property>
  </action>
  <action name="action_get_raw_image_size">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Get Raw Image Size</string>
   </property>
   <property name="toolTip">
    <string>get the size of raw image to be uploaded</string>
   </property>
  </action>
  <action name="action_upload_raw_image">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Upload Raw Image</string>
   </property>
   <property name="toolTip">
    <string>upload raw image to host</string>
   </property>
  </action>
  <action name="action_get_alg_image_size">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Get Algorithm Image Size</string>
   </property>
   <property name="toolTip">
    <string>get the size of algorithm image to be uploaded</string>
   </property>
  </action>
  <action name="action_upload_alg_image">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Upload Algorithm Image</string>
   </property>
   <property name="toolTip">
    <string>upload algorithm image to host</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
