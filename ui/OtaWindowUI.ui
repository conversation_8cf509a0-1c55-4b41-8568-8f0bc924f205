<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OTAWindowUI</class>
 <widget class="QMainWindow" name="OTAWindowUI">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>373</width>
    <height>333</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>OTA test tool</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QTextEdit" name="otastatus_textEdit">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>90</y>
      <width>331</width>
      <height>141</height>
     </rect>
    </property>
   </widget>
   <widget class="QPushButton" name="selectfile_btn">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>10</y>
      <width>99</width>
      <height>31</height>
     </rect>
    </property>
    <property name="sizePolicy">
     <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
      <horstretch>0</horstretch>
      <verstretch>0</verstretch>
     </sizepolicy>
    </property>
    <property name="text">
     <string>selectfile</string>
    </property>
   </widget>
   <widget class="QLabel" name="selectfile_label">
    <property name="geometry">
     <rect>
      <x>300</x>
      <y>30</y>
      <width>61</width>
      <height>21</height>
     </rect>
    </property>
    <property name="text">
     <string/>
    </property>
   </widget>
   <widget class="QProgressBar" name="ota_progressBar">
    <property name="geometry">
     <rect>
      <x>120</x>
      <y>240</y>
      <width>231</width>
      <height>31</height>
     </rect>
    </property>
    <property name="value">
     <number>0</number>
    </property>
   </widget>
   <widget class="QPushButton" name="startOTA_btn">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>240</y>
      <width>91</width>
      <height>31</height>
     </rect>
    </property>
    <property name="sizePolicy">
     <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
      <horstretch>0</horstretch>
      <verstretch>0</verstretch>
     </sizepolicy>
    </property>
    <property name="text">
     <string>start</string>
    </property>
    <property name="iconSize">
     <size>
      <width>16</width>
      <height>16</height>
     </size>
    </property>
   </widget>
   <widget class="QRadioButton" name="txt_radio_Button">
    <property name="geometry">
     <rect>
      <x>280</x>
      <y>10</y>
      <width>112</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>.txt</string>
    </property>
    <property name="checked">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QRadioButton" name="ota_radio_Button">
    <property name="geometry">
     <rect>
      <x>280</x>
      <y>30</y>
      <width>112</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>.ota</string>
    </property>
   </widget>
   <widget class="QRadioButton" name="json_radio_Button">
    <property name="geometry">
     <rect>
      <x>280</x>
      <y>50</y>
      <width>112</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>.json</string>
    </property>
   </widget>
   <widget class="QComboBox" name="bps_cbx">
    <property name="geometry">
     <rect>
      <x>170</x>
      <y>10</y>
      <width>91</width>
      <height>25</height>
     </rect>
    </property>
   </widget>
   <widget class="QLabel" name="label">
    <property name="geometry">
     <rect>
      <x>130</x>
      <y>10</y>
      <width>41</width>
      <height>17</height>
     </rect>
    </property>
    <property name="text">
     <string>bps:</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_2">
    <property name="geometry">
     <rect>
      <x>130</x>
      <y>50</y>
      <width>41</width>
      <height>17</height>
     </rect>
    </property>
    <property name="text">
     <string>pkt:</string>
    </property>
   </widget>
   <widget class="QComboBox" name="pkt_cbx">
    <property name="geometry">
     <rect>
      <x>170</x>
      <y>50</y>
      <width>91</width>
      <height>25</height>
     </rect>
    </property>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>373</width>
     <height>28</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
