# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'Send<PERSON>ileWindowUI.ui'
#
# Created by: PyQt5 UI code generator 5.15.1
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_SendFileWindow(object):
    def setupUi(self, SendFileWindow):
        SendFileWindow.setObjectName("SendFileWindow")
        SendFileWindow.setWindowModality(QtCore.Qt.ApplicationModal)
        SendFileWindow.resize(487, 334)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(SendFileWindow.sizePolicy().hasHeightForWidth())
        SendFileWindow.setSizePolicy(sizePolicy)
        self.centralwidget = QtWidgets.QWidget(SendFileWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.select_file_btn = QtWidgets.QPushButton(self.centralwidget)
        self.select_file_btn.setGeometry(QtCore.QRect(20, 20, 111, 31))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.select_file_btn.sizePolicy().hasHeightForWidth())
        self.select_file_btn.setSizePolicy(sizePolicy)
        self.select_file_btn.setObjectName("select_file_btn")
        self.select_file_label = QtWidgets.QLabel(self.centralwidget)
        self.select_file_label.setGeometry(QtCore.QRect(140, 10, 311, 41))
        self.select_file_label.setText("")
        self.select_file_label.setObjectName("select_file_label")
        self.start_send_file_btn = QtWidgets.QPushButton(self.centralwidget)
        self.start_send_file_btn.setGeometry(QtCore.QRect(50, 210, 91, 51))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.start_send_file_btn.sizePolicy().hasHeightForWidth())
        self.start_send_file_btn.setSizePolicy(sizePolicy)
        self.start_send_file_btn.setIconSize(QtCore.QSize(16, 16))
        self.start_send_file_btn.setObjectName("start_send_file_btn")
        self.text_refresh_textedit = QtWidgets.QPlainTextEdit(self.centralwidget)
        self.text_refresh_textedit.setGeometry(QtCore.QRect(40, 80, 401, 121))
        self.text_refresh_textedit.setObjectName("text_refresh_textedit")
        self.write_file_to_fs_btn = QtWidgets.QPushButton(self.centralwidget)
        self.write_file_to_fs_btn.setGeometry(QtCore.QRect(260, 210, 89, 51))
        self.write_file_to_fs_btn.setObjectName("write_file_to_fs_btn")
        SendFileWindow.setCentralWidget(self.centralwidget)
        self.menubar = QtWidgets.QMenuBar(SendFileWindow)
        self.menubar.setGeometry(QtCore.QRect(0, 0, 487, 22))
        self.menubar.setObjectName("menubar")
        SendFileWindow.setMenuBar(self.menubar)
        self.statusbar = QtWidgets.QStatusBar(SendFileWindow)
        self.statusbar.setObjectName("statusbar")
        SendFileWindow.setStatusBar(self.statusbar)

        self.retranslateUi(SendFileWindow)
        QtCore.QMetaObject.connectSlotsByName(SendFileWindow)

    def retranslateUi(self, SendFileWindow):
        _translate = QtCore.QCoreApplication.translate
        SendFileWindow.setWindowTitle(_translate("SendFileWindow", "Send File"))
        self.select_file_btn.setText(_translate("SendFileWindow", "select file"))
        self.start_send_file_btn.setText(_translate("SendFileWindow", "start"))
        self.write_file_to_fs_btn.setText(_translate("SendFileWindow", "write to fs"))
