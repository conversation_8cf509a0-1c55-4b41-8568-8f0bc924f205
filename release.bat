@echo off
setlocal enabledelayedexpansion

REM Release script for HI10 app (Windows)
echo Building HI10 Tool for Windows...

REM Set version and timestamp
set version=v1.0.0_
for /f "tokens=2 delims==" %%i in ('wmic OS Get localdatetime /value') do set datetime=%%i
set time=%datetime:~0,4%_%datetime:~4,2%_%datetime:~6,2%

REM Get git commit ID (if git is available)
for /f %%i in ('git rev-parse --short HEAD 2^>nul') do set commit_id=%%i
if not defined commit_id set commit_id=unknown

REM Configuration
set app_name=HI10_Tool_!version!!time!
set appfile=HI10Win.py
set logo_path=resource\logo.ico

REM Create version string file
echo HI10_Tool_version:!version!!commit_id! > version_str
echo app_name:!app_name! >> version_str

REM Check if required files exist
echo Checking required files...
if not exist "localization\hi10_app.qm" (
    echo Warning: Translation file localization\hi10_app.qm not found!
    echo The application may not have Chinese translation support.
)
if not exist "resource\logo.ico" (
    echo Warning: Logo file resource\logo.ico not found!
)
if not exist "ui\MainWindow.py" (
    echo Warning: UI file ui\MainWindow.py not found!
)

echo Building: !app_name!

REM Build the application
python -m PyInstaller ^
    --hidden-import=pyttsx3.drivers ^
    --hidden-import=pyttsx3.drivers.dummy ^
    --hidden-import=pyttsx3.drivers.espeak ^
    --hidden-import=pyttsx3.drivers.nsss ^
    --hidden-import=pyttsx3.drivers.sapi5 ^
    --hidden-import=pynput.keyboard._win32 ^
    --hidden-import=PyQt5.QtCore ^
    --hidden-import=PyQt5.QtWidgets ^
    --hidden-import=PyQt5.QtGui ^
    --hidden-import=qasync ^
    --hidden-import=serial ^
    --hidden-import=numpy ^
    --hidden-import=cv2 ^
    --icon="%logo_path%" ^
    --add-data "version_str;." ^
    --add-data "resource;resource" ^
    --add-data "localization\hi10_app.qm;localization" ^
    --add-data "localization\hi10_app.ts;localization" ^
    --add-data "ui;ui" ^
    -p "%cd%\src" ^
    -F -w "%appfile%" ^
    -n "!app_name!"

REM Check if build was successful
if %errorlevel% neq 0 (
    echo.
    echo Build failed with error code %errorlevel%
    pause
    exit /b %errorlevel%
)

REM Clean up
if exist "*.spec" del /q "*.spec"
if exist "build" rmdir /s /q "build"

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo Executable: dist\!app_name!.exe

REM Check if executable exists and show size
if exist "dist\!app_name!.exe" (
    echo.
    echo Executable size:
    for %%A in ("dist\!app_name!.exe") do echo   %%~zA bytes
    echo.
    echo Files included in the package:
    echo   - Python runtime and all dependencies
    echo   - PyQt5 GUI framework
    echo   - Translation files ^(Chinese support^)
    echo   - Application resources ^(icons, UI files^)
    echo   - Version information
    echo.
    echo The executable is completely self-contained.
    echo Users do NOT need to install Python or any dependencies.
) else (
    echo.
    echo ERROR: Executable not found in dist folder!
    echo Build may have failed.
)

echo.
echo Press any key to exit...
pause >nul