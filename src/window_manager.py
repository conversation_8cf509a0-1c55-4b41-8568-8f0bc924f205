#! /usr/bin/env python3
# -*- coding: utf-8 -*-
from PyQt5 import QtWidgets
from PyQt5.QtCore import Qt, QObject, pyqtSignal, pyqtSlot
from PyQt5.QtWidgets import Q<PERSON>ain<PERSON>indow, QLabel, QWidget,\
    QPushButton, QLineEdit, QPlainTextEdit, QTableView, \
    QFileDialog, QDialog
from PyQt5.QtGui import QTextCursor, QColor, QColorConstants

class WindowManager:

    def __init__(self, *args, **kwargs) -> None:
        self.facelock = args[0]
        class ChangeWindowStateSignal(QObject):
            signal = pyqtSignal(name='receiveDataProcess')
        self.close_window_signal = ChangeWindowStateSignal()

    def closeEvent(self, event) -> None:
        self.close_window_signal.signal.emit()

    def set_window_position_to_middle(self, window):
        screen  =   QtWidgets.QDesktopWidget().screenGeometry()
        size    =   window.geometry()
        window.move(int((screen.width()-size.width())/2), int((screen.height()-size.height())/2))

    def disable_ui(self, ui_array) -> None:
        for ui in ui_array:
            ui.setEnabled(False)

    def enable_ui(self, ui_array) -> None:
        for ui in ui_array:
            ui.setEnabled(True)

    def on_selectfile_btn_pressed(self, path, label, textedit) -> None:
        file_name = QFileDialog.getOpenFileName(QDialog(), "Open File", "", "All (*.*)")
        path[0] = file_name[0]
        label.setText(path[0])
        label.adjustSize()
        label.setAlignment(Qt.AlignTop)
        label.setWordWrap(True)

    def window_textedit_text_change(self, line_cnt, textedit, text_str, result_code):
        try:
            line_cnt[0] += 1
        except AttributeError as e:
            line_cnt[0] = 1
        result_colors = [QColorConstants.DarkGreen, QColorConstants.Black, QColorConstants.DarkRed, QColorConstants.DarkYellow]
        color = result_colors[result_code]
        tmp_color = f'{color.red():02x}{color.green():02x}{color.blue():02x}'
        info = f'<span style=" color:#{tmp_color};" >'
        info += f'{line_cnt[0]}:{text_str}'
        info += "</span>"
        # self.received_textedit.appendPlainText(info)
        textedit.appendHtml(info)