#! /usr/bin/env python3
# -*- coding: utf-8 -*-
from PyQt5 import QtWidgets, QtCore
from PyQt5.QtCore import Qt, QObject, pyqtSignal, pyqtSlot
from PyQt5.QtWidgets import QMain<PERSON>indow, QLabel, QWidget,\
    QPushButton, QLineEdit, QApplication, QPlainTextEdit, QTableView, \
    QFileDialog, QDialog
from PyQt5.QtGui import QTextCursor
from qasync import QEventLoop, asyncSlot, asyncClose
from .hi10_protocol import *
from ui.SendFileWindowUI import Ui_SendFileWindow
from .window_manager import WindowManager
from .sync_api import HI10
import functools
import os
import math
import hashlib
# from time import time
import time
import asyncio

class SendFileWindow(QMainWindow, Ui_SendFileWindow, WindowManager):

    def __init__(self, *args, **kwargs) -> None:
        super().__init__()
        WindowManager.__init__(self, *args, **kwargs)
        self.facelock = args[0]
        # self.facelock.refresh_textedit_signal.signal.connect(self.sendfile_textedit_status_change)
        self.setupUi(self)
        self.line_cnt = [0]
        self.file_path = [""]
        self.set_window_position_to_middle(self)
        self.setFixedSize(self.geometry().width(),self.geometry().height())
        self.all_func_ui = [self.start_send_file_btn, self.select_file_btn,]
        self.store_type = StoreType.TYPE_MEM
        self._translate = QtCore.QCoreApplication.translate

    def on_select_file_btn_pressed(self) -> None:
        self.on_selectfile_btn_pressed(self.file_path, \
                                        self.select_file_label, \
                                        self.text_refresh_textedit)
        text = self._translate('SendFileLog', 'choose file:\n') + self.file_path[0] + "\n"
        self.file_name = self.file_path[0].split('/')
        self.window_textedit_text_change(text, FaceLock.ReturnCode.INFO)

    def handleSendImageUI(fn):
        """disable ui when enter, and enable ui when exit"""
        @functools.wraps(fn)
        def wrapper(self, *args, **kwargs):
            self.disable_ui(self.all_func_ui)
            f = asyncio.ensure_future(fn(self, *args, **kwargs))
            while not f.done():
                QApplication.instance().processEvents()
            self.enable_ui(self.all_func_ui)
            return
        return wrapper

    @handleSendImageUI
    @asyncSlot()
    async def on_start_send_file_btn_pressed(self) -> None:
        sendfile = SendFile(self.facelock)
        sendfile.set_store_type(self.store_type)
        sendfile.refresh_textedit_signal.signal.connect(self.window_textedit_text_change)
        await sendfile.send_file(self.facelock, self.file_path[0])

    @asyncSlot()
    async def on_write_file_to_fs_btn_pressed(self) -> None:
        ret_code, result = await self.facelock.send_file_info(self.file_name[-1], timeout_second = 3)
        if ret_code == FaceLock.ReturnCode.SUCCESS:
            self.window_textedit_text_change(self._translate('SendFileLog','success'), ret_code)
        else:
            self.window_textedit_text_change(result[2], ret_code)


    @pyqtSlot(str, int)
    def window_textedit_text_change(self, text_str, result_code):
        WindowManager.window_textedit_text_change(self, self.line_cnt, \
                                    self.text_refresh_textedit, \
                                    text_str, \
                                    result_code)

class SendFile():

    """class used to send file"""
    def __init__(self, *args, **kwargs) -> None:
        self.facelock = args[0]
        class RefreshTexteditSignal(QObject):
            signal = pyqtSignal(str, int, name='refreshTexteditStatus')
        self.refresh_textedit_signal = RefreshTexteditSignal()
        self.store_type = StoreType.TYPE_MEM
        self._translate = QtCore.QCoreApplication.translate

    def set_store_type(self, store_type):
        self.store_type = store_type

    def check_input(self, file_path) -> bool:
        if(os.path.isfile(file_path) == False):
            self.refresh_textedit_signal.signal.emit(self._translate('SendFileLog', 'please choose a file!\n'), FaceLock.ReturnCode.INFO)
            return False
        self.file_size = os.path.getsize(file_path)
        print(self.file_size)
        if(self.file_size > 1280 * 720 * 3):
            self.refresh_textedit_signal.signal.emit(self._translate('SendFileLog','file is too big!\n'), FaceLock.ReturnCode.INFO)
            return False
        return True

    @asyncSlot()
    async def send_file(self, facelock, file_path):
        if(self.check_input(file_path) == False):
            return False, "check file path error"

        print(f'file size is {self.file_size}')
        offset = 0
        pid = 0
        with open(file_path, 'rb'):
            for data in self.get_file_data(file_path):
                packet_size = len(data)
                pid += 1
                print(f'packet id is {pid}')
                ret_code, result = await facelock.send_file_data(self.store_type, self.file_size, offset, packet_size, data, timeout_second=5)
                if ret_code != FaceLock.ReturnCode.SUCCESS:
                    print('send file error!')
                    self.text_str = self._translate('SendFileLog','send file error!')
                    self.refresh_textedit_signal.signal.emit(self.text_str, ret_code)
                    return False, "send file error"
                else:
                    offset += packet_size
                    self.refresh_textedit_signal.signal.emit(self._translate('SendFileLog', 'successfully send data, pid =') + str(pid), ret_code)

        # send file end packet -> offset=0 and packet_size=0
        print(f'send end packet')
        data = bytearray(0)
        ret_code, result = await self.facelock.send_file_data(self.store_type, self.file_size, 0, 0, data, timeout_second=5)
        if ret_code != FaceLock.ReturnCode.SUCCESS:
            print('send end packet error!')
            self.refresh_textedit_signal.signal.emit(self._translate('SendFileLog','send file error!\n'), ret_code)
            return False, "send end packet error"
        else:
            self.refresh_textedit_signal.signal.emit(self._translate('SendFileLog','successfully send end packet!\n'), ret_code)
            return True, "success"


    def get_file_data(self, filePath, block_size=1024):
        with open(filePath, 'rb') as file:
            while True:
                data = file.read(block_size)
                if not data:
                    return
                yield data
