#!/usr/bin/env python3
# -*- coding: utf-8 -*-


import configparser
import os

class GlobalConfigParam:
    CFG_ENABLE_TIME_STATISTIC = False
    CFG_ENABLE_STATUS_REPORT = True
    CFG_ENABLE_SPEAKER = True
    CFG_ENABLE_SEND_FILE = False
    CFG_SAME_MSG_CD_TIME_SECOND = 4
    CFG_ENABLE_SEND_CMD = True

def load_cfg(ini_file_name:str = ".aiva_hi10.ini", config_param_class=None):
    """
    加载配置文件并更新GlobalConfigParam类的属性

    Args:
        ini_file_name: 配置文件名
        config_param_class: 要更新的GlobalConfigParam类，如果为None则使用本模块的GlobalConfigParam
    """
    if config_param_class is None:
        config_param_class = GlobalConfigParam

    # cfg_file_name = os.path.join(cmte.get_program_source_directory(), ini_file_name)
    cfg_file_name = os.path.join(ini_file_name)
    config = configparser.ConfigParser()

    config.read(cfg_file_name)
    cfg_sections = config.sections()
    if 'default' in config.sections():
        default_cfg = config['default']
        if 'enable_ui_state_decorator' in default_cfg:
            config_param_class.CFG_ENABLE_SEND_CMD = True if default_cfg['enable_send_cmd'] in ['True', 'true', 'yes'] else False
    else:
        config['default'] = {'enable_send_cmd': GlobalConfigParam.CFG_ENABLE_SEND_CMD,
                            }

    with open(cfg_file_name, 'w') as configfile:
        config.write(configfile)
