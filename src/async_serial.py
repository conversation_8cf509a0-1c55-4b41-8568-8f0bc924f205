#! /usr/bin/env python3
# -*- coding: utf-8 -*-

from PyQt5.QtCore import Qt, pyqtSignal, QObject, pyqtSlot, QThread, QMutex, QMutexLocker, QWaitCondition, QTimer, QTime, QSignalBlocker
import traceback
import sys

class WorkerSignals(QObject):
    '''
    Defines the signals available from a running reader thread.

    Supported signals are:

    finished
        No data

    error
        `tuple` (exctype, value, traceback.format_exc() )

    result
        `object` data returned from processing, anything

    progress
        `int` indicating % progress

    '''
    finished = pyqtSignal()
    error = pyqtSignal(tuple)
    result = pyqtSignal(object, bytes)
    progress = pyqtSignal(int)


# TODO check reader thread should handle the serial open/close?
class SerialReader(QThread):
    def __init__(self, serial_wrapper):
        QThread.__init__(self)
        self.serial_wrapper = serial_wrapper
        # TODO pyQt not implement QtAtomicInt, so we use QMutex here
        self.mutex = QMutex()
        self.running = True
        self.signals = WorkerSignals()

    # will call from another thread
    def stop(self):
        with QMutexLocker(self.mutex):
            self.running = False

    def need_stop(self):
        with QMutexLocker(self.mutex):
            return not self.running

    def run(self):
        while True:
            if self.need_stop():
                with QMutexLocker(self.mutex):
                    self.running = True # set runnint to true, so next time we call start() can work correctly
                break
            
            result_msg = None
            raw_data = None
            try:
                # recv_msg() should return a tuple (msg, raw_data) or None
                recv_tuple = self.serial_wrapper.recv_msg()
                if recv_tuple:
                    result_msg, raw_data = recv_tuple
            except Exception:
                traceback.print_exc()
                exctype, value = sys.exc_info()[:2]
                self.signals.error.emit((exctype, value, traceback.format_exc()))
                self.usleep(10)
                continue

            if result_msg:
                emit_data = bytes(raw_data) if raw_data is not None else bytes()
                self.signals.result.emit(result_msg, emit_data)
            else:
                self.usleep(1)

class SerialWriter(QThread):
    def __init__(self, serial_wrapper):
        QThread.__init__(self)
        self.serial_wrapper = serial_wrapper
        # TODO pyQt not implement QtAtomicInt, so we use QMutex here
        self.mutex = QMutex()
        self.running = True
        self.signals = WorkerSignals()
        #
        # queue is a python module which has implemented multithread lock semantic
        # but here we wanto use condition variable to offload cpu loads, if we use
        # condition variable with python queue, we may have change to dead lock due
        # to there are 2 different lock, one is the mutex work with condition variable
        # another is provided by queue module
        # self.msgs = queue.Queue()
        self.msgs = []
        self.queue_mutex = QMutex()
        self.condi = QWaitCondition()

    # will call from another thread
    def stop(self):
        with QMutexLocker(self.mutex):
            self.running = False
        self.condi.wakeAll()

    def need_stop(self):
        with QMutexLocker(self.mutex):
            return not self.running

    def commit_msg(self, msg):
        with QMutexLocker(self.queue_mutex):
            self.msgs.append(msg)
        self.condi.wakeAll()

    def run(self):
        while True:
            if self.need_stop():
                with QMutexLocker(self.mutex):
                    self.running = True # set runnint to true, so next time we call start() can work correctly
                break
            # we only lock to get msg, after got msg we release mutex immediately
            # thus we will not block mew msg commit when serial_wrapper is sending data

            # pending new message commit
            with QMutexLocker(self.queue_mutex):
                while len(self.msgs) == 0:
                    if self.need_stop():
                        break
                    self.condi.wait(self.queue_mutex, 100000)
            # process message
            while True:
                if self.need_stop():
                    break
                msg = None
                with QMutexLocker(self.queue_mutex):
                    if len(self.msgs) != 0:
                        msg = self.msgs[0]
                        self.msgs = self.msgs[1:]
                    else:
                        break
                if msg is not None:
                    try:
                        self.serial_wrapper.send_msg(msg)
                    except:
                        traceback.print_exc()
                        exctype, value = sys.exc_info()[:2]
                        self.signals.error.emit((exctype, value, traceback.format_exc()))
