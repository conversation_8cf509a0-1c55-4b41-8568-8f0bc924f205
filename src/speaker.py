from PyQt5.QtCore import Qt, pyqtSignal, QObject, pyqtSlot, QThread, QMutex, QMutexLocker, QWaitCondition, QTimer, QTime, QSignalBlocker

class Speaker(QThread):
    class ContentPriority:
        PRIORITY_EMERGENCY = 0
        PRIORITY_NORMAL = 1

    def __init__(self, same_msg_cd_time_second: float):
        QThread.__init__(self)
        # init pyttsx3
        import pyttsx3
        self.speaker = pyttsx3.init()
        self.speaker.setProperty('voice', 'zh')
        self.speaker.setProperty('rate', 280)
        # TODO pyQt not implement QtAtomicInt, so we use QMutex here
        self.mutex = QMutex()
        self.running = True
        #
        self.content_mutex = QMutex()
        self.speak_content = []
        #
        self.say_time = None
        self.cd_time_second = same_msg_cd_time_second

    def set_voice_rate(self, rate:int):
        self.speaker.setProperty('rate', rate)

    # will call from another thread
    def stop(self):
        with QMutexLocker(self.mutex):
            self.running = False

    def need_stop(self):
        with QMutexLocker(self.mutex):
            return not self.running

    def say_normal(self, content):
        # cold down time
        if self.say_time is not None:
            if self.say_time.elapsed() <= self.cd_time_second * 1000 and content == self.previous_content:
                print(f'speaker cold dowm time')
                return
            self.say_time.restart()
            self.previous_content = content
            self.say((self.ContentPriority.PRIORITY_NORMAL, content))
        else:
            self.say_time = QTime()
            self.say_time.start()
            self.previous_content = content
            self.say((self.ContentPriority.PRIORITY_NORMAL, content))

    def say_emergency(self, content):
        self.say((self.ContentPriority.PRIORITY_EMERGENCY, content))

    def say(self, content):
        with QMutexLocker(self.content_mutex):
            #
            if len(self.speak_content) < 2 and (content[0] == self.ContentPriority.PRIORITY_NORMAL):
                self.speak_content.append(content[1])
            elif content[0] == self.ContentPriority.PRIORITY_EMERGENCY:
                self.speak_content.clear()
                self.speak_content.append(content[1])


    def run(self):
        while True:
            # TODO current is poll mode, sleep some while. it's better to use async serial
            # but it seams currently not support well in python, use QSerial is an optional way
            if self.need_stop():
                with QMutexLocker(self.mutex):
                    self.running = True # set runnint to true, so next time we call start() can work correctly
                break
            # get speak content
            content = u''
            with QMutexLocker(self.content_mutex):
                if len(self.speak_content) != 0:
                    content = self.speak_content.pop(0)
            if (content != u''):
                self.speaker.say(content)
                self.speaker.runAndWait()

            self.msleep(1)


class SpeakerStub(QThread):
    class ContentPriority:
        PRIORITY_EMERGENCY = 0
        PRIORITY_NORMAL = 1

    def __init__(self, same_msg_cd_time_second: float):
        pass
        QThread.__init__(self)
        # # init pyttsx3
        # self.speaker = pyttsx3.init()
        # # TODO pyQt not implement QtAtomicInt, so we use QMutex here
        self.mutex = QMutex()
        self.running = True
        # #
        # self.content_mutex = QMutex()
        # self.speak_content = []
        # #
        # self.say_time = None

    # will call from another thread
    def stop(self):
        with QMutexLocker(self.mutex):
            self.running = False

    def need_stop(self):
        with QMutexLocker(self.mutex):
            return not self.running

    def say_normal(self, content):
        # cold down time
        # if self.say_time is not None:
        #     if self.say_time.elapsed() <= 1000 and content == self.previous_content:
        #         print(f'speaker cold dowm time')
        #         return
        #     self.say_time.restart()
        #     self.previous_content = content
        #     self.say((self.ContentPriority.PRIORITY_NORMAL, content))
        # else:
        #     self.say_time = QTime()
        #     self.say_time.start()
        #     self.previous_content = content
        #     self.say((self.ContentPriority.PRIORITY_NORMAL, content))
        pass

    def say_emergency(self, content):
        # self.say((self.ContentPriority.PRIORITY_EMERGENCY, content))
        pass

    def say(self, content):
        # with QMutexLocker(self.content_mutex):
        #     #
        #     if len(self.speak_content) < 2 and (content[0] == self.ContentPriority.PRIORITY_NORMAL):
        #         self.speak_content.append(content[1])
        #     elif content[0] == self.ContentPriority.PRIORITY_EMERGENCY:
        #         self.speak_content.clear()
        #         self.speak_content.append(content[1])
        pass


    def run(self):
        while True:
            # TODO current is poll mode, sleep some while. it's better to use async serial
            # but it seams currently not support well in python, use QSerial is an optional way
            if self.need_stop():
                break
            # get speak content
            # content = u''
            # with QMutexLocker(self.content_mutex):
            #     if len(self.speak_content) != 0:
            #         content = self.speak_content.pop(0)
            # if (content != u''):
                # pass
                # self.speaker.say(content)
                # self.speaker.runAndWait()

            self.msleep(1)
