#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# Protocol definition based on 《舜宇 HWP001 单目摄像头模块规格书》 v1.0 2025.05.19

from Cryptodome.Util.Padding import pad
from PyQt5 import QtCore
import enum
import logging
logger = logging.getLogger(__name__)

USER_NAME_SIZE = 32
FILE_NAME_SIZE = 32
VERSION_INFO_BUFFER_SIZE = 32
MAX_USER_COUNTS = 20
FEATURE_SIZE = 512

class MsgID:
    """Communication message ID definitions, based on spec section 6.3"""
    _translate = QtCore.QCoreApplication.translate
    # Module to Host (m->h)
    MID_REPLY                   = (0x00, _translate('Protocol', '模块对主控命令的应答'))
    MID_NOTE                    = (0x01, _translate('Protocol', '模块主动发给主控的信息'))
    MID_IMAGE                   = (0x02, _translate('Protocol', '模块给主控传送图片'))
    # Host to Module (h->m)
    MID_RESET                   = (0x10, _translate('Protocol', '停止当前处理，模块进入 standby'))
    MID_GETSTATUS               = (0x11, _translate('Protocol', '立即返回模块当前状态'))
    MID_LABEL_RECOG             = (0x12, _translate('Protocol', '标签识别'))
    MID_SET_THRESHOLD_LEVEL     = (0x13, _translate('Protocol', '设置识别阈值等级'))
    MID_GET_THRESHOLD_LEVEL     = (0x14, _translate('Protocol', '获取识别阈值等级'))
    MID_GET_FW_VERSION          = (0x15, _translate('Protocol', '获得固件版本信息'))
    MID_GET_LIB_MODEL_VERSION   = (0x16, _translate('Protocol', '获得算法库与模型库版本信息'))
    MID_GET_LOGFILE_SIZE        = (0x17, _translate('Protocol', '获取日志文件大小'))
    MID_UPLOAD_LOGFILE          = (0x18, _translate('Protocol', '上传日志文件到主控'))
    MID_GET_RAW_IMAGE_SIZE      = (0x19, _translate('Protocol', '获取待上传 raw 图片大小'))
    MID_UPLOAD_RAW_IMAGE        = (0x1a, _translate('Protocol', '上传 raw 图片到主控'))
    MID_GET_ALG_IMAGE_SIZE      = (0x1b, _translate('Protocol', '获取待上传算法小图大小'))
    MID_UPLOAD_ALG_IMAGE        = (0x1c, _translate('Protocol', '上传算法小图到主控'))
    MID_CONFIG_BAUDRATE         = (0x20, _translate('Protocol', 'OTA 模式下设定通信口波特率'))
    MID_START_OTA               = (0x21, _translate('Protocol', '进入 OTA 升级模式'))
    MID_STOP_OTA                = (0x22, _translate('Protocol', '退出 OTA 模式，模组重启'))
    MID_GET_OTA_STATUS          = (0x23, _translate('Protocol', '获取 OTA 状态及传输起始包序号'))
    MID_OTA_HEADER              = (0x24, _translate('Protocol', '发送升级包元数据'))
    MID_OTA_PACKET              = (0x25, _translate('Protocol', '发送升级包数据'))
    MID_POWERDOWN               = (0xED, _translate('Protocol', '模块下电'))
    MID_CMD_PARSE               = (0xFF, _translate('Protocol', 'cmd parse'))

    @staticmethod
    def description(msg_id):
        members = [attr for attr in dir(MsgID) if not callable(getattr(MsgID, attr)) and not attr.startswith("_")]
        for name in members:
            value = getattr(MsgID, name)
            if isinstance(value, tuple) and value[0] == msg_id:
                return value[1]
        return 'Unknown MsgID'

class Notify(object):
    class NotifyID:
        NID_READY                    =           0   # module ready for handling request (command)
        NID_LABEL_STATE              =           1   # the detected face status description
        NID_UNKNOWNERROR             =           2   # unknown error
        NID_OTA_DONE                 =           3   # OTA upgrading processing done

    class LabelState(object):
        LABEL_STATE_NORMAL                      = 0
        LABEL_STATE_NOLABEL                     = 1
        LABEL_STATE_TOOFAR                      = 2
        LABEL_STATE_TOOCLOSE                    = 3
        LABEL_STATE_TOOUP                       = 4
        LABEL_STATE_TOODOWN                     = 5
        LABEL_STATE_TOOLEFT                     = 6
        LABEL_STATE_TOORIGHT                    = 7
        LABEL_STATE_NO_ICON                     = 8
        LABEL_STATE_AE_ERROR                    = 9
        LABEL_STATE_UNKNOWN_REASON              = 10
        label_state_str = {
            f"{LABEL_STATE_NORMAL}"          : ('LABEL_STATE_NORMAL',          u''),                # normal state, the face is available
            f"{LABEL_STATE_NOLABEL}"         : ('LABEL_STATE_NOLABEL',         u'未检测到图标'),      # no face detected
            f"{LABEL_STATE_TOOUP}"           : ('LABEL_STATE_TOOUP',           u'偏高了请向下一点'),   # face is too up side
            f"{LABEL_STATE_TOODOWN}"         : ('LABEL_STATE_TOODOWN',         u'太低了请向上一点'),   # face is too down side
            f"{LABEL_STATE_TOOLEFT}"         : ('LABEL_STATE_TOOLEFT',         u'偏右了请向左一点'),   # face is too left side
            f"{LABEL_STATE_TOORIGHT}"        : ('LABEL_STATE_TOORIGHT',        u'偏左了请向右一点'),   # face is too right side
            f"{LABEL_STATE_TOOFAR}"          : ('LABEL_STATE_TOOFAR',          u'离太远了请靠近一点'), # face is too far
            f"{LABEL_STATE_TOOCLOSE}"        : ('LABEL_STATE_TOOCLOSE',        u'太靠近了请远离一点'), # face is too near
            f"{LABEL_STATE_NO_ICON}"         : ('LABEL_STATE_NO_ICON',         u'没有图标'),          # no icon# eyebrow occlusion
            f"{LABEL_STATE_AE_ERROR}"        : ('LABEL_STATE_AE_ERROR',        u'AE错误'),           # eye occlusion
            f"{LABEL_STATE_UNKNOWN_REASON}"  : ('LABEL_STATE_UNKNOWN_REASON',  u'未知错误'),          # face occlusion
        }

        def __init__(self):
            super().__init__()
            self.state  = 0x00 # uint8_t corresponding to label_state_str

        def serialize(self):
            pass

        def parse(self, array):
            if (len(array) != 1):
                print(f'error length of array for LabelState')
            self.state  = array[0]
            return self
        
class CmdParse(object):
    class CmdCode:
        EXEC_SHELL_CMDS          =  0
        SNAP_CAPTURE_CFG         =  1
        ENTER_LENSE_FOCUS_MODE   =  2
        SNAP_CAPTURE_CFG2        =  3
        IMAGE_ENROLL_EXT         =  5
        AUDIO_LOOPBACK_TEST      =  10
        XNN_WGT_CHECKSUM_VERIFY  =  11
        AUDIO_SPK_PLAY_TEST      =  12
        GET_MEAN_VALUE           =  13
        GET_CFG_FILE_VERSION     =  14
        GET_COMMON_FILE          =  15
        UPLOAD_IMAGE             =  20
        VERIFY_EXT               =  21
        GET_SAFETY_LEVEL         =  22
        SET_SENSOR_MF_MODE       =  30
        GET_PALM_VEIN_STATUS     =  40
        USER_ID_SAFETY_VERIFY    =  50
        GET_DATA                 =  254
        TEST_CMD                 =  255

    class ExecShellCmds(object):
        def __init__(self, cmd_len : int, cmd_str : str):
            super().__init__()
            self.msg_id = CmdParse.CmdCode.EXEC_SHELL_CMDS
            self.cmd_len = cmd_len
            self.cmd_str = cmd_str

        def serialize(self):
            # NOTE: we can not append cmd_len here, due to protocol design
            array = bytearray(0)
            array += str.encode(self.cmd_str)
            return array
        
    def __init__(self, sub_cmd):
        super().__init__()
        self.sub_cmd = sub_cmd
        self.msg_id         =  MsgID.MID_CMD_PARSE[0]

    def serialize(self):
        self.cmd_code       =  self.sub_cmd.msg_id
        self.cmd_len        =  self.sub_cmd.cmd_len if isinstance(self.sub_cmd, CmdParse.ExecShellCmds) else 0x00
        self.reserved       = 0x00
        array = bytearray(0)
        array.append(self.cmd_code & 0xFF)
        array.append(self.cmd_len & 0xFF)
        array.append(self.reserved & 0xFF)
        array += self.sub_cmd.serialize()
        return array


    class OTAState(object):
        def __init__(self):
            super().__init__()
            self.result                   =    0x00  #0 :is sucess; 1: is faile;

        def parse(self, array):
            self.result = array
            return self

    def __init__(self):
        super().__init__()
        self.id                      =          0x00
        self.notify_payload          =          []

    def serialize(self):
        pass

    def parse(self, array):
        self.id = array[0]
        if self.id == self.NotifyID.NID_READY:
            return self.id, None
        elif self.id == self.NotifyID.NID_LABEL_STATE:
            label_state = self.LabelState().parse(array[1:])
            return self.id, label_state
        elif self.id == self.NotifyID.NID_UNKNOWNERROR:
            return self.id, None
        elif self.id == self.NotifyID.NID_OTA_DONE:
            ota_state = self.OTAState().parse(array[1])
            return self.id, ota_state
        else:
            return None, None

class Reset(object):
    def __init__(self):
        super().__init__()
        self.msg_id                = MsgID.MID_RESET[0]

    def serialize(self):
        array = bytearray(0)
        return array

    def parse(self, array):
        pass

class GetStatus(object):
    def __init__(self):
        self.msg_id = MsgID.MID_GETSTATUS[0]
    def serialize(self):
        return bytearray(0)

class LabelRecog(object):
    def __init__(self, timeout_seconds=20):
        self.msg_id = MsgID.MID_LABEL_RECOG[0]
        self.timeout_seconds = timeout_seconds
    def serialize(self):
        return bytearray([self.timeout_seconds & 0xFF])

class GetFWVersion(object):
    def __init__(self):
        self.msg_id = MsgID.MID_GET_FW_VERSION[0]
    def serialize(self):
        return bytearray(0)

class GetLibModelVersion(object):
    def __init__(self):
        self.msg_id = MsgID.MID_GET_LIB_MODEL_VERSION[0]
    def serialize(self):
        return bytearray(0)

class SetThresholdLevel(object):
    def __init__(self, threshold_level):
        self.msg_id = MsgID.MID_SET_THRESHOLD_LEVEL[0]
        self.threshold_level = threshold_level
    def serialize(self):
        return bytearray([self.threshold_level])

class GetThresholdLevel(object):
    def __init__(self):
        self.msg_id = MsgID.MID_GET_THRESHOLD_LEVEL[0]
    def serialize(self):
        return bytearray(0)

class GetLogfileSize(object):
    def __init__(self):
        self.msg_id = MsgID.MID_GET_LOGFILE_SIZE[0]
    def serialize(self):
        return bytearray(0)

class GetRawImageSize(object):
    def __init__(self):
        self.msg_id = MsgID.MID_GET_RAW_IMAGE_SIZE[0]
    def serialize(self):
        return bytearray(0)

class GetAlgImageSize(object):
    def __init__(self):
        self.msg_id = MsgID.MID_GET_ALG_IMAGE_SIZE[0]
    def serialize(self):
        return bytearray(0)

class UploadLogfile(object):
    def __init__(self, offset, size):
        self.msg_id = MsgID.MID_UPLOAD_LOGFILE[0]
        self.offset = offset
        self.size = size
    def serialize(self):
        array = bytearray(0)
        array += self.offset.to_bytes(4, byteorder='big')
        array += self.size.to_bytes(4, byteorder='big')
        return array

class UploadRawImage(object):
    def __init__(self, offset, size):
        self.msg_id = MsgID.MID_UPLOAD_RAW_IMAGE[0]
        self.offset = offset
        self.size = size
    def serialize(self):
        array = bytearray(0)
        array += self.offset.to_bytes(4, byteorder='big')
        array += self.size.to_bytes(4, byteorder='big')
        return array

class UploadAlgImage(object):
    def __init__(self, offset, size):
        self.msg_id = MsgID.MID_UPLOAD_ALG_IMAGE[0]
        self.offset = offset
        self.size = size
    def serialize(self):
        array = bytearray(0)
        array += self.offset.to_bytes(4, byteorder='big')
        array += self.size.to_bytes(4, byteorder='big')
        return array

Change_Baudrate_To_Protocol_Number = {
115200 :1,
230400:2,
460800:3,
1500000:4,
19200:8
}

# serial port speed negotiation with firmware
class ConfigBaudrate(object):
    def __init__(self, baudrate):
        super().__init__()
        self.msg_id                = MsgID.MID_CONFIG_BAUDRATE[0]
        self.baudrate              = Change_Baudrate_To_Protocol_Number[baudrate]

    def serialize(self):
        array = bytearray(0)
        array.append(self.baudrate)
        return array

# start ota data MID_START_OTA
class StartOTA(object):
    def __init__(self):
        super().__init__()
        self.msg_id                = MsgID.MID_START_OTA[0]
        self.version_primary       = 0              # uint8_t primary version number
        self.version_secondary     = 0              # uint8_t secondary version number
        self.version_revision      = 0              # uint8_t revision number

    def serialize(self):
        array = bytearray(0)
        array.append(self.version_primary & 0xFF)
        array.append(self.version_secondary & 0xFF)
        array.append(self.version_revision & 0xFF)
        return array

# get ota status
class GetOTAStatus(object):
    def __init__(self):
        super().__init__()
        self.msg_id                = MsgID.MID_GET_OTA_STATUS[0]
        self.ota_status            = 0              # uint8_t current ota status
        self.next_pid_e            = bytearray(2)   # uint8_t expected next pid

    def serialize(self):
        array = bytearray(0)
        array.append(self.ota_status & 0xFF)
        array += self.next_pid_e
        return array

# send ota header
class SendOTAHeader(object):
    def __init__(self, filesize, pktsize, pktnumber, hashcode):
        super().__init__()
        self.msg_id                = MsgID.MID_OTA_HEADER[0]
        self.fsize_b               = filesize.to_bytes(4, byteorder='big')      # uint8_t OTA FW file size int ‐> [b1, b2, b3, b4]
        self.num_packet            = pktnumber.to_bytes(4, byteorder='big')     # uint8_t number packet to be divided for transferring, int ‐> [b1, b2, b3, b4]
        self.pkt_size              = pktsize.to_bytes(2, byteorder='big')       # uint8_t raw data size of single packet
        # self.md5_sum               = binascii.hexlify(str.encode(hashcode))   # uint8_t md5 check sum
        # self.md5_sum = int(hashcode, 16).to_bytes(32, byteorder='big')
        self.md5_sum               = bytearray()
        for ch in hashcode:
            self.md5_sum += int(ch,16).to_bytes(1, byteorder='big')

    def serialize(self):
        array = bytearray(0)
        array += self.fsize_b
        array += self.num_packet
        array += self.pkt_size
        array += self.md5_sum
        return array

class SendOTAPacket(object):
    def __init__(self, pid, psize, data):
        super().__init__()
        self.msg_id                = MsgID.MID_OTA_PACKET[0]
        self.pid                   = pid.to_bytes(2, byteorder='big')           # uint8_t the packet id
        self.psize                 = psize.to_bytes(2, byteorder='big')         # uint8_t the size of this package
        self.data                  = data                                       # uint8_t data

    def serialize(self):
        array = bytearray(0)
        array += self.pid
        array += self.psize
        array += self.data
        return array

class StopOTA(object):
    def __init__(self):
        super().__init__()
        self.msg_id                = MsgID.MID_STOP_OTA[0]

    def serialize(self):
        array = bytearray(0)
        return array

class StoreType():
    TYPE_MEM                      = 0
    TYPE_FS                       = 1

class SendFilePacket(object):
    def __init__(self, store_type, file_size, offset, packet_size, data):
        super().__init__()
        self.msg_id                = MsgID.MID_TRANS_FILE_PACKET[0]
        self.store_type            = 0x00                                              # uint8_t store type
        self.file_size             = file_size.to_bytes(4, byteorder='big')            # uint8_t file size
        self.offset                = offset.to_bytes(4, byteorder='big')               # uint8_t file offset
        self.psize                 = packet_size.to_bytes(2, byteorder='big')          # uint8_t packet size
        self.file_data             = data                                              # uint8_t file data

    def serialize(self):
        array = bytearray(0)
        array.append(self.store_type & 0xFF)
        array += self.file_size
        array += self.offset
        array += self.psize
        array += self.file_data
        return array

class GetLibVersion(object):
    def __init__(self):
        super().__init__()
        self.msg_id                 = MsgID.MID_GET_LIBRARY_VERSION[0]
    
    def serialize(self):
        array = bytearray(0)
        return array

class GetSNInfo(object):
    def __init__(self):
        super().__init__()
        self.msg_id                 = MsgID.MID_GET_SN[0]
    
    def serialize(self):
        array = bytearray(0)
        return array

class Capture(object):
    def __init__(self):
        super().__init__()
        self.msg_id                = MsgID.MID_CAPTURE[0]

    def serialize(self):
        array = bytearray(0)
        return array

    def parse(self, array):
        pass

class Snap(object):
    def __init__(self, image_counts, start_number):
        super().__init__()
        self.msg_id                = MsgID.MID_SNAPIMAGE[0]
        self.image_counts          = image_counts
        self.start_number          = start_number

    def serialize(self):
        array = bytearray(0)
        array.append(self.image_counts & 0xFF)
        array.append(self.start_number & 0xFF)
        return array

    def parse(self, array):
        pass

class GetSavedImgSize(object):
    def __init__(self, image_number):
        super().__init__()
        self.msg_id                = MsgID.MID_GETSAVEDIMAGE[0]
        self.image_number          = image_number

    def serialize(self):
        array = bytearray(0)
        array.append(self.image_number & 0xFF)
        return array

    def parse(self, array):
        pass

class GetRawImgSize(object):
    def __init__(self, image_number):
        super().__init__()
        self.msg_id                = 0x19

    def serialize(self):
        array = bytearray(0)
        return array

    def parse(self, array):
        pass

class GetData(object):
    def __init__(self, offset, size, msg_id):
        super().__init__()
        self.msg_id                = msg_id
        self.offset                = bytearray(0)
        self.size                  = bytearray(0)
        #
        self.offset.append((offset>>24) & 0xFF)
        self.offset.append((offset>>16) & 0xFF)
        self.offset.append((offset>>8) & 0xFF)
        self.offset.append((offset>>0) & 0xFF)
        #
        self.size.append((size>>24) & 0xFF)
        self.size.append((size>>16) & 0xFF)
        self.size.append((size>>8) & 0xFF)
        self.size.append((size>>0) & 0xFF)

    def serialize(self):
        array = self.offset
        for ch in self.size:
            array.append(ch)

        return array

    def parse(self, array):
        pass

class FileInfo(object):
    def __init__(self, file_name):
        super().__init__()
        self.msg_id         =   MsgID.MID_FILE_SAVE_TO_FS[0]
        self.name_len       = len(file_name)
        self.file_name      = file_name
    
    def serialize(self):
        array = bytearray(0)
        array.append(self.name_len & 0xFF)
        array += str.encode(self.file_name)
        print(array)
        return array
    
    def parse(self, array):
        pass

class GetLogFileSize(object):
    class LogType:
        LOG_FILE_KERNEL        = 0  # kernel log
        LOG_FILE_SENSELOCK_APP = 1  # senselock app log
        LOG_FILE_FACE_MODULE   = 2  # face module log

    def __init__(self, log_type):
        super().__init__()
        self.msg_id                = MsgID.MID_GET_LOGFILE_SIZE[0]
        self.log_type              = log_type

    def serialize(self):
        array = bytearray(0)
        array.append(self.log_type & 0xFF)
        return array

    def parse(self, array):
        pass

class GetDebugInfoSize(object):
    def __init__(self) -> None:
        super().__init__()
        self.msg_id                = MsgID.MID_GET_DEBUG_INFO[0]

    def serialize(self):
        array = bytearray(0)
        return array

    def parse(self, array):
        pass


class CmdParse(object):
    class CmdCode:
        EXEC_SHELL_CMDS          =  0
        XNN_WGT_CHECKSUM_VERIFY  =  1

    class ExecShellCmds(object):
        def __init__(self, cmd_len : int, cmd_str : str):
            super().__init__()
            self.msg_id = CmdParse.CmdCode.EXEC_SHELL_CMDS
            self.cmd_len = cmd_len
            self.cmd_str = cmd_str

        def serialize(self):
            # NOTE: we can not append cmd_len here, due to protocol design
            array = bytearray(0)
            array += str.encode(self.cmd_str)
            return array

    class XnnWgtChecksumVerify(object):
        def __init__(self):
            super().__init__()
            self.msg_id = CmdParse.CmdCode.XNN_WGT_CHECKSUM_VERIFY

        def serialize(self):
            array = bytearray(0)
            return array


    @classmethod
    def from_seperate_params(self, cmd_code : CmdCode, cmd_len, data):
        sub_cmd = None
        if cmd_code != self.CmdCode.EXEC_SHELL_CMDS:
            data = [int(x.strip()) for x in data.split()]

        if cmd_code == self.CmdCode.EXEC_SHELL_CMDS:
            sub_cmd = self.ExecShellCmds(cmd_len, data)
        elif cmd_code == self.CmdCode.XNN_WGT_CHECKSUM_VERIFY:
            sub_cmd = self.XnnWgtChecksumVerify()
        assert(sub_cmd is not None)
        #
        return CmdParse(sub_cmd)

    def __init__(self, sub_cmd):
        super().__init__()
        self.sub_cmd = sub_cmd
        self.msg_id         =  MsgID.MID_CMD_PARSE[0]

    def serialize(self):
        self.cmd_code       =  self.sub_cmd.msg_id
        self.cmd_len        =  self.sub_cmd.cmd_len if isinstance(self.sub_cmd, CmdParse.ExecShellCmds) else 0x00
        self.reserved       = 0x00
        array = bytearray(0)
        array.append(self.cmd_code & 0xFF)
        array.append(self.cmd_len & 0xFF)
        array.append(self.reserved & 0xFF)
        array += self.sub_cmd.serialize()
        return array

# power down
class PowerDown(object):
    def __init__(self) -> None:
        super().__init__()
        self.msg_id             = MsgID.MID_POWERDOWN[0]
    
    def serialize(self):
        array = bytearray(0)
        return array

    def parse(self, array):
        pass


# localization is not compatible if we set return_code_str as global variable
class ret_code_str:
    def __init__(self) -> None:
        self._translate = QtCore.QCoreApplication.translate
        self.return_code_str = [
            (0,         'MR_SUCCESS',                     self._translate('ProtocolRetStr','success')),                                      # = 0
            (1,         'MR_REJECTED',                    self._translate('ProtocolRetStr','module rejected this command')),                 # = 1
            (2,         'MR_ABORTED',                     self._translate('ProtocolRetStr','algo aborted')),                                 # = 2
            (3,         'NOT_DEFINE_THIS_CODE',           self._translate('ProtocolRetStr','padding code, protocol not define this code')),  # = 3
            (4,         'MR_FAILED4_CAMERA',              self._translate('ProtocolRetStr','camera open failed')),                           # = 4
            (5,         'MR_FAILED4_UNKNOWNREASON',       self._translate('ProtocolRetStr','unknown error')),                                # = 5
            (6,         'MR_FAILED4_INVALIDPARAM',        self._translate('ProtocolRetStr','invalid parameter')),                            # = 6
            (7,         'MR_FAILED4_NOMEMORY',            self._translate('ProtocolRetStr','no enough memory')),                             # = 7
            (11,        'NOT_DEFINE_THIS_CODE',           self._translate('ProtocolRetStr','padding code, protocol not define this code')),  # = 11
            (13,        'MR_FAILED4_TIMEOUT',             self._translate('ProtocolRetStr','exceed the time limit')),                        # = 13
            (19,        'MR_FAILED4_READ_FILE',           self._translate('ProtocolRetStr','read file failed')),                             # = 19
            (20,        'MR_FAILED4_WRITE_FILE',          self._translate('ProtocolRetStr','write file failed')),                            # = 20
        ]

    def transfer(self, code:int) -> str:
        for item in self.return_code_str:
            if item[0] == code:
                return item[2]
        return ""

    def get_item(self, code:int):
        for item in self.return_code_str:
            if item[0] == code:
                return item
        return (0, "", "")

class Reply(object):
    # /* message result code */
    # typedef uint8_t s_msg_result;
    @staticmethod
    def get_reply_result(return_code):
        return ret_code_str().get_item(return_code)


    def __init__(self):
        super().__init__()
        self.msgid                         =             0x00 # uint8_t the command(message) id to reply (the request message ID)
        self.return_code                   =             0x00 # uint8_t command handling result: success or failed ->s_msg_result
        self.msg_payload                   =             []

    def serialize(self):
        pass

    def parse(self, array):
        if len(array) < 2:
            return None, None
        self.msgid = array[0]
        self.return_code = array[1]
        self.msg_payload = array[2:] if len(array) > 2 else []

        if (self.return_code != 0):
            print(f'error {ret_code_str().transfer(self.return_code)}')
            return self.msgid, self

        # Specific Reply Parsers
        if self.msgid == MsgID.MID_LABEL_RECOG[0]:
            return self.msgid, self.ReplyRecognize().parse(self.msg_payload)
        elif self.msgid in [MsgID.MID_GET_RAW_IMAGE_SIZE[0], MsgID.MID_GET_ALG_IMAGE_SIZE[0], MsgID.MID_GET_LOGFILE_SIZE[0]]:
            return self.msgid, self.ReplyDataSize().parse(self.msg_payload)
        elif self.msgid == MsgID.MID_GET_FW_VERSION[0]:
            return self.msgid, self.ReplyGetVersion().parse(self.msg_payload)
        elif self.msgid == MsgID.MID_GET_LIB_MODEL_VERSION[0]:
            return self.msgid, self.ReplyGetVersion().parse(self.msg_payload) # Assuming same structure
        elif self.msgid == MsgID.MID_GETSTATUS[0]:
            return self.msgid, self.ReplyGetStatus().parse(self.msg_payload)
        elif self.msgid == MsgID.MID_GET_OTA_STATUS[0]:
            return self.msgid, self.ReplyGetOTAStatus().parse(self.msg_payload)
        elif self.msgid == MsgID.MID_GET_THRESHOLD_LEVEL[0]:
            return self.msgid, self.ReplyGetThreshold().parse(self.msg_payload)
        elif self.msgid == MsgID.MID_UPLOAD_LOGFILE[0]:
            return self.msgid, self.ReplyUploadLogfile().parse(self.msg_payload)
        # Simple ACK replies
        elif self.msgid in [
            MsgID.MID_RESET[0], MsgID.MID_UPLOAD_RAW_IMAGE[0], MsgID.MID_UPLOAD_ALG_IMAGE[0],
            MsgID.MID_UPLOAD_LOGFILE[0], MsgID.MID_START_OTA[0], MsgID.MID_OTA_HEADER[0],
            MsgID.MID_OTA_PACKET[0], MsgID.MID_STOP_OTA[0], MsgID.MID_POWERDOWN[0],
            MsgID.MID_CONFIG_BAUDRATE[0], MsgID.MID_SET_THRESHOLD_LEVEL[0]
        ]:
            return self.msgid, self
        else:
            print(f'TODO: Unhandled reply for msgid: {self.msgid:#02x}')
            return self.msgid, self

    class ReplyGetStatus(object):
        class ModuleState:
            MS_STANDBY                  =            0   # IDLE, waiting for commands
            MS_BUSY                     =            1   # in working of processing commands
            MS_ERROR                    =            2   # in error state. can't work properl
            MS_INVALID                  =            3   # not initialize
            MS_OTA                      =            4   # in ota state
            @staticmethod
            def module_state_str(state: int) -> str:
                _module_state_str = [
                    'STANDBY',
                    'BUSY',
                    'ERROR',
                    'INVALID',
                    'OTA'
                ]
                try:
                    return _module_state_str[state]
                except IndexError as e:
                    return 'INVALID'

        def __init__(self):
            super().__init__()
            self.status = self.ModuleState.MS_INVALID

        def serialize(self, array):
            pass

        def parse(self, array):
            if len(array) < 1:
                print(f'error length when parse ReplyGetStatus')
                return None
            else:
                self.status = array[0]
            return self

    class ReplyRecognize(object):
        def __init__(self):
            self.temperature_level = 0
            self.with_under_cloth = 0
        def parse(self, array):
            if len(array) >= 2:
                self.temperature_level = array[0]
                self.with_under_cloth = array[1]
            return self

    class ReplyDataSize(object):
        def __init__(self):
            self.size = 0
        def parse(self, array):
            if len(array) >= 4:
                self.size = int.from_bytes(array[:4], 'big')
            return self
        
    class ReplyUploadLogfile(object):
        def __init__(self):
            self.uploaded_data = bytearray(0)
        def parse(self, array):
            self.uploaded_data = bytearray(array)
            return self

    class ReplyGetVersion(object):
        def __init__(self):
            self.version_str = ''
        def parse(self, array):
            self.version_str = bytes(array).decode('utf-8', errors='ignore')
            return self

    class ReplyGetOTAStatus(object):
        def __init__(self):
            super().__init__()
            self.otaStatus              = 0x00 # current ota status
            self.nextExpectedPID        = 0x0000 # expected next pid, [b0,b1]

        def parse(self, array):
            if len(array) < 3:
                print(f'error len of reply get user info')
                return None
            else:
                self.otaStatus = array[0] & 0xFF
                self.nextExpectedPID = array[2] << 8 | array[1]
                return self

    class ReplyGetThreshold(object):
        def __init__(self):
            self.threshold_level = 0
        def parse(self, array):
            if len(array) >= 1:
                self.threshold_level = array[0]
            return self

class Msg(object):
    def __init__(self, syncwh, syncwl, raw_data=None):
        super().__init__()
        self.syncwh = syncwh
        self.syncwl = syncwl
        self.msg_id = 0
        self.msg_payload = []

    def serialize(self):
        payload_len = len(self.msg_payload)
        array = bytearray(0)
        # .--------.--------.-----.----------.------.--------------------------.
        # | SYNC_H | SYNC_L | MID | DataSize | Data | crc of MID DataSize Data |
        # '--------'--------'-----'----------'------'--------------------------'
        array.append(self.syncwh & 0xFF)
        array.append(self.syncwl & 0xFF)
        array.append(self.msg_id & 0xFF)
        array += payload_len.to_bytes(2, 'big')
        
        crc_data = array[2:] + self.msg_payload
        crc = 0
        for byte in crc_data:
            crc ^= byte
        
        array += self.msg_payload
        array.append(crc & 0xFF)
        
        logger.debug(f"Sending: {array.hex()}")
        return array

    def parse(self, raw_data=None):
        if raw_data:
            print(f"Received: {raw_data.hex()}")
        
        if self.msg_id == MsgID.MID_REPLY[0]:
            msg_id, reply = Reply().parse(self.msg_payload)
            return self.msg_id, msg_id, reply
        elif self.msg_id == MsgID.MID_NOTE[0]:
            notify_id, notify = Notify().parse(self.msg_payload)
            return self.msg_id, notify_id, notify
        elif self.msg_id == MsgID.MID_IMAGE[0]:
            # The spec is not clear on MID_IMAGE payload, handle as raw data
            return self.msg_id, 0, self.msg_payload
        else:
            print(f'Unknown msg_id received: {self.msg_id:#02x}')
            return None, None, None
