"""
图像抓取模块
"""

import os
import numpy as np
from datetime import datetime
from PyQt5.QtGui import QPixmap, QImage
import logging
logger = logging.getLogger(__name__)

# 常见图像分辨率配置
COMMON_RESOLUTIONS = [
    (640, 480),     # VGA (4:3)
    (480, 640),     # VGA 反转
    (800, 600),     # SVGA (4:3)
    (600, 800),     # SVGA 反转
    (1024, 768),    # XGA (4:3)
    (768, 1024),    # XGA 反转
    (1280, 720),    # HD (16:9)
    (720, 1280),    # HD 反转
    (1600, 1200),   # UXGA (4:3)
    (1200, 1600),   # UXGA 反转
    (1920, 1080),   # Full HD (16:9)
    (1080, 1920),   # Full HD 反转
    (512, 512),     # 正方形
    (320, 240),     # QVGA
]

# 特殊尺寸映射
SPECIAL_SIZE_MAP = {
    1920000: (1600, 1200),  # UXGA RAW
    3840000: (1600, 1200),  # UXGA YUV422 (1600*1200*2)
}

# YUV格式尺寸映射 (数据长度 -> (宽度, 高度))
YUV422_SIZE_MAP = {
    # YUV422格式：每像素2字节 (Y=1字节 + UV=1字节)
    327680: (256,640),      # 256*640*2 
    614400: (640, 480),     # VGA YUV422 (640*480*2)
    960000: (800, 600),     # SVGA YUV422 (800*600*2)
    1572864: (1024, 768),   # XGA YUV422 (1024*768*2)
    1843200: (1280, 720),   # HD YUV422 (1280*720*2)
    3840000: (1600, 1200),  # UXGA YUV422 (1600*1200*2)
    4147200: (1920, 1080),  # Full HD YUV422 (1920*1080*2)
}

# 文件夹映射配置
FOLDER_MAP = {
    "raw_image": "raw_images",
    "alg_image": "alg_images",
}

# 波特率配置
HIGH_BAUDRATE = 1500000  # 适合图像传输的波特率
DEFAULT_BAUDRATE = 19200


async def configure_baudrate_for_image_transfer(hi10):
    """配置高速波特率用于图像传输"""
    try:
        ret_code, result = await hi10.config_baudrate(HIGH_BAUDRATE, timeout_second=5)
        if ret_code == hi10.ReturnCode.SUCCESS:
            hi10.change_baudrate(HIGH_BAUDRATE)
            logger.debug(f"Successfully set high baudrate: {HIGH_BAUDRATE}")
            return True
        else:
            logger.warning(f"Failed to set high baudrate: {result}")
            return False
    except Exception as e:
        logger.error(f"Error configuring baudrate: {e}")
        return False


async def restore_baudrate(hi10):
    """恢复默认波特率"""
    try:
        ret_code, result = await hi10.config_baudrate(DEFAULT_BAUDRATE, timeout_second=5)
        if ret_code == hi10.ReturnCode.SUCCESS:
            hi10.change_baudrate(DEFAULT_BAUDRATE)
            logger.debug(f"Successfully restored baudrate: {DEFAULT_BAUDRATE}")
        else:
            logger.warning(f"Failed to restore baudrate: {result}")
    except Exception as e:
        logger.error(f"Error restoring baudrate: {e}")


# 图像格式检测配置
IMAGE_FORMAT_SIGNATURES = {
    'JPEG': ([0xFF, 0xD8], [0xFF, 0xD9]),
    'PNG': ([0x89, 0x50, 0x4E, 0x47], [0x49, 0x45, 0x4E, 0x44]),
    'BMP': ([0x42, 0x4D], None),
    'TIFF_LE': ([0x49, 0x49, 0x2A, 0x00], None),
    'TIFF_BE': ([0x4D, 0x4D, 0x00, 0x2A], None),
}


def detect_image_format(image_data: bytes):
    """
    统一的图像格式检测函数

    Returns:
        格式字符串: "jpeg", "png", "bmp", "tiff", "yuv422", "raw", 或 None
    """
    data_len = len(image_data)

    # 1. 检测标准图像格式 (JPEG, PNG, BMP, TIFF)
    if data_len >= 4:
        for format_name, (header, footer) in IMAGE_FORMAT_SIGNATURES.items():
            if len(image_data) >= len(header) and list(image_data[:len(header)]) == header:
                if footer is None or (len(image_data) >= len(footer) and list(image_data[-len(footer):]) == footer):
                    return format_name.lower()

    # 2. 检测YUV422格式
    if data_len in YUV422_SIZE_MAP:
        width, height = YUV422_SIZE_MAP[data_len]
        if data_len == width * height * 2:
            return "yuv422"

    # 3. 检测RAW格式 (特殊尺寸映射)
    if data_len in SPECIAL_SIZE_MAP:
        width, height = SPECIAL_SIZE_MAP[data_len]
        if data_len == width * height:
            return "raw"

    # 4. 检测常见分辨率的RAW格式
    for width, height in COMMON_RESOLUTIONS:
        if data_len == width * height:
            return "raw"

    # 5. 智能检测 (优先RAW，因为更常见)
    # 检查是否可能是RAW格式 (完全平方数附近)
    sqrt_len = int(data_len ** 0.5)
    for w in range(max(100, sqrt_len - 50), sqrt_len + 50):
        if data_len % w == 0:
            h = data_len // w
            # 检查宽高比是否合理
            ratio = max(w, h) / min(w, h)
            if 1.0 <= ratio <= 3.0:  # 合理的宽高比
                return "raw"

    return None


def YUV422_to_rgb(YUV422_data: bytes, width: int, height: int):
    """
    将YUV422格式数据转换为RGB格式
    参考show422sp.py的实现

    Args:
        YUV422_data: YUV422格式的原始数据
        width: 图像宽度
        height: 图像高度

    Returns:
        RGB格式的numpy数组 (height, width, 3)
    """
    try:
        yuv_array = np.frombuffer(YUV422_data, dtype=np.uint8)

        y_size = width * height
        uv_size = y_size

        # 检查数据长度
        expected_size = y_size + uv_size
        if len(yuv_array) < expected_size:
            logger.warning(f"YUV data size {len(yuv_array)} < expected {expected_size}")
            return None

        # 分离Y和UV分量
        y = yuv_array[0:y_size].reshape((height, width))
        uv = yuv_array[y_size:y_size + uv_size].reshape((height, width))  # Interleaved U, V

        # 分离U和V分量
        u = uv[:, 0::2]  # 取偶数列作为U
        v = uv[:, 1::2]  # 取奇数列作为V

        # U和V上采样到与Y相同的尺寸
        u = np.repeat(u, 2, axis=1).astype(np.float32)
        v = np.repeat(v, 2, axis=1).astype(np.float32)
        y = y.astype(np.float32)

        # YUV到RGB转换 (BT.601标准)
        c = y - 16
        d = u - 128
        e = v - 128

        r = (1.164 * c + 1.596 * e)
        g = (1.164 * c - 0.392 * d - 0.813 * e)
        b = (1.164 * c + 2.017 * d)

        # 限制到有效范围并转换为uint8
        r = np.clip(r, 0, 255).astype(np.uint8)
        g = np.clip(g, 0, 255).astype(np.uint8)
        b = np.clip(b, 0, 255).astype(np.uint8)

        # 组合为RGB图像
        rgb = np.stack((r, g, b), axis=-1)

        logger.debug(f"Successfully converted YUV422 to RGB: {width}x{height}")
        return rgb

    except Exception as e:
        logger.error(f"Error converting YUV422 to RGB: {e}")
        return None


def detect_yuv_format(image_data: bytes):
    """检测是否为YUV422格式"""
    data_len = len(image_data)
    logger.debug(f"Checking YUV format for data length: {data_len}")

    # 检查是否匹配YUV422尺寸
    if data_len in YUV422_SIZE_MAP:
        width, height = YUV422_SIZE_MAP[data_len]
        expected_size = width * height * 2
        logger.debug(f"Found YUV422 mapping: {width}x{height}, expected_size: {expected_size}")
        if data_len == expected_size:
            logger.info(f"Detected YUV422 format: {width}x{height}")
            return width, height
        else:
            logger.warning(f"Size mismatch: data_len={data_len}, expected={expected_size}")
    else:
        logger.debug(f"Data length {data_len} not found in YUV422_SIZE_MAP")

    return None, None


async def read_image_internal(hi10, image_type: str):
    """

    Args:
        hi10: HI10协议实例
        image_type: "raw" 或 "alg"

    Returns:
        (ret_code, image_data, processed_pixmap)
    """
    logger.info(f"Reading {image_type} image...")

    # 配置和恢复波特率
    success = await configure_baudrate_for_image_transfer(hi10)
    if not success:
        logger.warning("Failed to configure high baudrate, continuing with default")

    try:
        # 读取图像数据
        if image_type == "raw":
            ret_code, image_data = await hi10.read_raw_image(timeout_second=20)
        else:
            ret_code, image_data = await hi10.read_alg_image(timeout_second=20)
    finally:
        # 确保恢复波特率
        await restore_baudrate(hi10)

    # -------------------- 处理图像数据 --------------------
    processed_pixmap = None
    if ret_code == hi10.ReturnCode.SUCCESS:
        processed_pixmap = process_image_data(image_data, image_type)

    return ret_code, image_data, processed_pixmap


def detect_and_try_image_sizes(test_data, offset=0):
    """统一的图像尺寸检测和尝试函数"""
    data_len = len(test_data)

    # 1. 检查特殊尺寸映射
    if data_len in SPECIAL_SIZE_MAP:
        width, height = SPECIAL_SIZE_MAP[data_len]
        return try_render_image(test_data, width, height, f"special size {width}x{height} with offset {offset}")

    # 2. 尝试常见分辨率
    for width, height in COMMON_RESOLUTIONS:
        if data_len >= width * height:
            pixmap = try_render_image(test_data, width, height, f"common size {width}x{height} with offset {offset}")
            if pixmap:
                return pixmap

    # 3. 智能检测尺寸
    possible_sizes = []
    max_w = int(data_len ** 0.5)

    for w in range(max_w, 100, -1):
        if data_len % w == 0:
            h = data_len // w
            ratio = w / h

            # 匹配4:3 (1.333) 或16:9 (1.777)
            if 1.3 <= ratio <= 1.4 or 1.7 <= ratio <= 1.8:
                possible_sizes.append((w, h, ratio))

            # 同时添加宽高反转的情况
            ratio_reversed = h / w
            if 1.3 <= ratio_reversed <= 1.4 or 1.7 <= ratio_reversed <= 1.8:
                possible_sizes.append((h, w, ratio_reversed))

    if possible_sizes:
        # 按宽高比接近程度排序
        possible_sizes.sort(key=lambda x: abs(x[2] - 1.333) if x[2] < 1.5 else abs(x[2] - 1.777))
        for width, height, _ in possible_sizes:
            if data_len >= width * height:
                pixmap = try_render_image(test_data, width, height, f"smart detected {width}x{height} with offset {offset}")
                if pixmap:
                    return pixmap

    # 4. 最后尝试默认尺寸
    width = int(data_len ** 0.5)
    height = data_len // width

    # 检查宽高反转是否更合理
    ratio = width / height
    ratio_reversed = height / width

    if (1.3 <= ratio_reversed <= 1.4 or 1.7 <= ratio_reversed <= 1.8) and ratio > 2:
        width, height = height, width

    return try_render_image(test_data, width, height, f"fallback {width}x{height} with offset {offset}")


def try_render_image(test_data, width, height, description):
    """尝试渲染指定尺寸的图像"""
    try:
        if len(test_data) < width * height:
            return None

        img_data = test_data[:width * height].reshape((height, width))

        # 检查数据有效性
        if is_valid_image(img_data):
            # 增强对比度
            enhanced_img = enhance_image_contrast(img_data)

            pixmap = create_image_from_data(enhanced_img, width, height)
            if pixmap:
                logger.info(f'Image displayed successfully using {description}')
                return pixmap
    except Exception as e:
        logger.debug(f"Failed to render image with {description}: {e}")

    return None


def create_image_from_rgb_data(rgb_data, width, height):
    """从RGB数据创建QPixmap"""
    try:
        # 确保数据是连续的
        rgb_data = np.ascontiguousarray(rgb_data)

        # 创建QImage (RGB888格式)
        qimage = QImage(rgb_data.data, width, height, width * 3, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(qimage)

        if not pixmap.isNull():
            logger.debug(f"Successfully created RGB {width}x{height} pixmap")
            return pixmap
        else:
            logger.warning(f"Created null RGB pixmap for {width}x{height}")
            return None
    except Exception as e:
        logger.error(f"Failed to create RGB image: {str(e)}")
        return None


def process_as_yuv_image(image_data: bytes):
    """处理YUV422格式图像数据"""
    try:
        data_len = len(image_data)
        logger.info(f"YUV data length: {data_len} bytes")

        # 检测YUV格式和尺寸
        width, height = detect_yuv_format(image_data)
        if width and height:
            # 转换YUV到RGB
            rgb_data = YUV422_to_rgb(image_data, width, height)
            if rgb_data is not None:
                # 创建QPixmap
                pixmap = create_image_from_rgb_data(rgb_data, width, height)
                if pixmap:
                    logger.info(f"Successfully processed YUV422 image: {width}x{height}")
                    return pixmap

        logger.warning("Failed to process as YUV422 format")
        return None

    except Exception as e:
        logger.error(f"YUV image processing error: {str(e)}")
        return None


def process_image_data(image_data: bytes, image_type: str):
    """统一的图像数据处理函数"""
    try:
        data_len = len(image_data)
        logger.info(f'{image_type.upper()} data length: {data_len} bytes')

        # 对于算法图像，先尝试标准图像格式
        if image_type == "alg":
            # 显示数据的十六进制预览（前32字节）
            hex_preview = ' '.join([f'{b:02X}' for b in image_data[:32]])
            logger.debug(f'Algorithm data hex preview: {hex_preview}...')

            # 检测图像格式
            detected_format = detect_image_format(image_data)
            if detected_format:
                logger.info(f'Detected {detected_format} format')

                # 尝试加载为标准图像格式
                pixmap = QPixmap()
                if pixmap.loadFromData(image_data):
                    logger.info(f'Algorithm image loaded as {detected_format}')
                    return pixmap
                else:
                    logger.warning(f'Failed to load as {detected_format}, trying RAW processing')

        # 处理为RAW/YUV数据（对于raw图像直接处理，对于alg图像作为fallback）
        if image_type == "raw":
            return process_as_raw_or_yuv_image(image_data)
        else:
            # 算法图像标准格式失败后，尝试作为RAW/YUV处理
            logger.info('Trying to display algorithm data as RAW/YUV image')
            return process_as_raw_or_yuv_image_with_detection(image_data)

    except Exception as e:
        logger.error(f'{image_type.upper()} image processing error: {str(e)}')
        return None


def process_as_raw_or_yuv_image(image_data: bytes):
    """统一处理RAW或YUV格式图像数据"""
    try:
        data_len = len(image_data)
        logger.info(f"Image data length: {data_len} bytes")

        # 1. 首先尝试YUV422格式
        yuv_pixmap = process_as_yuv_image(image_data)
        if yuv_pixmap:
            logger.info("Successfully processed as YUV422 format")
            return yuv_pixmap

        # 2. 如果YUV失败，尝试RAW格式
        logger.info("YUV processing failed, trying RAW format")
        raw_pixmap = process_as_raw_image(image_data)
        if raw_pixmap:
            logger.info("Successfully processed as RAW format")
            return raw_pixmap

        logger.warning("Failed to process as both YUV and RAW formats")
        return None

    except Exception as e:
        logger.error(f"Raw/YUV image processing error: {str(e)}")
        return None


def process_as_raw_image(image_data: bytes):
    """处理标准RAW图像数据"""
    try:
        data_len = len(image_data)
        logger.info(f"RAW data length: {data_len} bytes")

        # 处理RAW数据
        img_array = np.frombuffer(image_data, dtype=np.uint8)

        # 使用统一的尺寸检测和渲染
        return detect_and_try_image_sizes(img_array)

    except Exception as e:
        logger.error(f"RAW image processing error: {str(e)}")
        return None


def process_as_raw_or_yuv_image_with_detection(image_data: bytes):
    """处理算法图像数据作为RAW或YUV图像，包含智能检测"""
    try:
        # 1. 首先尝试YUV422格式
        yuv_pixmap = process_as_yuv_image(image_data)
        if yuv_pixmap:
            logger.info("Successfully processed algorithm data as YUV422 format")
            return yuv_pixmap

        # 2. 如果YUV失败，使用原有的RAW检测逻辑
        logger.info("YUV processing failed, trying RAW format with detection")
        return process_as_raw_image_with_detection(image_data)

    except Exception as e:
        logger.error(f'Algorithm RAW/YUV processing error: {str(e)}')
        return None


def process_as_raw_image_with_detection(image_data: bytes):
    """处理算法图像数据作为RAW图像，包含智能检测"""
    try:
        data_len = len(image_data)

        # 分析数据特征
        img_array = np.frombuffer(image_data, dtype=np.uint8)
        data_stats = analyze_image_data_stats(img_array)

        logger.info(f'Algorithm data stats: min={data_stats["min"]}, max={data_stats["max"]}, '
                   f'mean={data_stats["mean"]:.1f}, std={data_stats["std"]:.1f}, '
                   f'unique={data_stats["unique_count"]}')

        # 尝试不同的数据偏移（可能有头部信息）
        for offset in [0, 4, 8, 16, 32, 64, 128, 256]:
            if data_len <= offset:
                continue

            test_data = img_array[offset:]

            # 尝试不同尺寸
            pixmap = detect_and_try_image_sizes(test_data, offset)
            if pixmap:
                return pixmap

        # 保存数据用于调试
        debug_file = save_debug_data(image_data, "alg")
        logger.warning(f'Failed to display algorithm image. Data saved to {debug_file} for analysis')
        return None

    except Exception as e:
        logger.error(f'Algorithm RAW processing error: {str(e)}')
        return None


def analyze_image_data_stats(img_array):
    """分析图像数据统计特征"""
    try:
        return {
            'min': np.min(img_array),
            'max': np.max(img_array),
            'mean': np.mean(img_array),
            'std': np.std(img_array),
            'unique_count': len(np.unique(img_array))
        }
    except Exception as e:
        logger.error(f"Error analyzing image data stats: {e}")
        return {'min': 0, 'max': 0, 'mean': 0, 'std': 0, 'unique_count': 0}


def save_debug_data(image_data: bytes, data_type: str):
    """保存调试数据"""
    try:
        debug_file = f"debug_{data_type}_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.bin"
        with open(debug_file, "wb") as f:
            f.write(image_data)
        logger.debug(f"Debug data saved to {debug_file}")
        return debug_file
    except Exception as e:
        logger.error(f"Error saving debug data: {e}")
        return None


def ensure_folder_exists(folder_name):
    """确保文件夹存在"""
    try:
        if not os.path.exists(folder_name):
            os.makedirs(folder_name)
            logger.debug(f"Created directory: {folder_name}")
    except Exception as e:
        logger.error(f"Error creating directory {folder_name}: {e}")


def save_image_data(pixmap, original_data, prefix, file_type, image_format="raw"):
    """保存图像数据（包括原始数据和PNG格式）"""
    try:
        folder_name = FOLDER_MAP.get(file_type, "images")
        ensure_folder_exists(folder_name)

        timestamp = datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
        base_filename = f"{prefix}_{timestamp}"

        saved_files = []

        # 1. 保存原始数据
        if original_data:
            # 根据图像格式确定扩展名
            if image_format == "yuv422":
                raw_ext = "yuv"
            elif image_format == "raw":
                raw_ext = "raw"
            else:
                raw_ext = "bin"  # 默认二进制格式

            raw_filename = f"{base_filename}.{raw_ext}"
            raw_filepath = os.path.join(folder_name, raw_filename)

            with open(raw_filepath, 'wb') as f:
                f.write(original_data)

            logger.info(f"Successfully saved original data: {raw_filepath}")
            saved_files.append(raw_filepath)

        # 2. 保存PNG格式
        if pixmap:
            png_filename = f"{base_filename}.png"
            png_filepath = os.path.join(folder_name, png_filename)

            success = pixmap.save(png_filepath, "PNG")
            if success:
                logger.info(f"Successfully saved PNG image: {png_filepath}")
                saved_files.append(png_filepath)
            else:
                logger.warning("Failed to save PNG image")

        return saved_files if saved_files else None

    except Exception as e:
        logger.error(f"Error saving image data: {e}")
        return None


def save_displayed_image_as_png(pixmap, prefix, file_type):
    """保存显示的图像为PNG格式（向后兼容函数）"""
    result = save_image_data(pixmap, None, prefix, file_type)
    return result[0] if result else None



def is_valid_image(img_data):
    """检查图像数据是否有效"""
    try:
        # 检查基本统计特征
        std_dev = np.std(img_data)
        unique_count = len(np.unique(img_data))

        # 有效图像应该有一定的变化和足够的唯一值
        return std_dev > 5 and unique_count > 10
    except:
        return False


def enhance_image_contrast(img_data):
    """增强图像对比度"""
    try:
        # 计算直方图拉伸
        min_val = np.percentile(img_data, 2)  # 使用2%和98%分位数避免极值影响
        max_val = np.percentile(img_data, 98)

        if max_val > min_val:
            # 线性拉伸
            enhanced = np.clip((img_data - min_val) / (max_val - min_val) * 255, 0, 255).astype(np.uint8)

            # 如果对比度仍然不够，应用伽马校正
            if np.std(enhanced) < 30:
                gamma = 0.7  # 增强对比度
                enhanced = np.power(enhanced / 255.0, gamma) * 255
                enhanced = enhanced.astype(np.uint8)

            return enhanced
        else:
            return img_data.astype(np.uint8)
    except:
        return img_data.astype(np.uint8)


def create_image_from_data(img_data, width, height):
    """从图像数据创建QPixmap"""
    try:
        # 确保数据是连续的
        img_data = np.ascontiguousarray(img_data)

        # 创建QImage
        qimage = QImage(img_data.data, width, height, width, QImage.Format_Grayscale8)
        pixmap = QPixmap.fromImage(qimage)

        if not pixmap.isNull():
            logger.debug(f"Successfully created {width}x{height} pixmap")
            return pixmap
        else:
            logger.warning(f"Created null pixmap for {width}x{height}")
            return None
    except Exception as e:
        logger.error(f"Failed to create image: {str(e)}")
        return None

