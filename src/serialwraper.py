#! /usr/bin/env python3
# -*- coding: utf-8 -*-
from .hi10_protocol import *
from PyQt5.QtCore import pyqtSignal, QObject
import serial
import re
import logging
logger = logging.getLogger(__name__)

class SerialErrorSignal(QObject):
    serial_error = pyqtSignal()

class SpecialDataRecvSignal(QObject):
    signal = pyqtSignal(str)

class SerialWraper:
    class ParseState:
        PENDING_SYNCWH = 0
        PENDING_SYNCWL = 1
        PENDING_MSGID = 2
        PENDING_SIZE_HI = 3
        PENDING_SIZE_LO = 4
        PENDING_DATA = 5
        PENDING_CHECK_CRC = 6

    def __init__(self, port):
        self.array = bytearray(0)
        self.port = port
        self.crc = 0x00
        #
        self.SYNC_WORDH = 0xEF
        self.SYNC_WORDL = 0xAA
        self.parse_state = self.ParseState.PENDING_SYNCWH
        self.recvmsg = Msg(self.SYNC_WORDH, self.SYNC_WORDL)
        self.error_count = 0
        self.signal = SerialErrorSignal()

    def change_baudrate(self, baudrate):
        if baudrate == self.port.baudrate:
            print(f'baudrate already set to {baudrate}, no need to change')
            return

        if self.port.is_open:
            self.port.close()
        self.port.baudrate = baudrate
        self.port.open()

    def parse(self, msg):
        while len(self.array) > 0:
            ch = self.array.pop(0)
            # parse state machine
            if (self.parse_state == self.ParseState.PENDING_SYNCWH):
                if (self.SYNC_WORDH == ch):
                    self.crc = 0
                    msg.msg_payload.clear()
                    self.parse_state += 1
            elif (self.parse_state == self.ParseState.PENDING_SYNCWL):
                if (self.SYNC_WORDL == ch):
                    self.parse_state += 1
            elif (self.parse_state == self.ParseState.PENDING_MSGID):
                msg.msg_id = ch
                self.crc ^= ch
                self.parse_state += 1
            elif (self.parse_state == self.ParseState.PENDING_SIZE_HI):
                msg.size_high_byte = ch
                self.crc ^= ch
                self.parse_state += 1
            elif (self.parse_state == self.ParseState.PENDING_SIZE_LO):
                msg.size_low_byte = ch
                self.crc ^= ch
                msg_len = (msg.size_high_byte << 8) | ch
                if (msg_len == 0):
                    self.parse_state = self.ParseState.PENDING_CHECK_CRC
                else:
                    self.parse_state += 1
            elif (self.parse_state == self.ParseState.PENDING_DATA):
                msg.msg_payload.append(ch) # append current byte
                self.crc ^= ch
                #
                msg_payload_len = (msg.size_high_byte << 8) | msg.size_low_byte
                copy_num = min(len(self.array), msg_payload_len - len(msg.msg_payload))
                for i in range(copy_num):
                    ch = self.array.pop(0)
                    self.crc ^= ch
                    msg.msg_payload.append(ch)
                if msg_payload_len == len(msg.msg_payload):
                    # msg receive complete, calculate crc
                    self.parse_state += 1
            elif (self.parse_state == self.ParseState.PENDING_CHECK_CRC):
                self.parse_state = self.ParseState.PENDING_SYNCWH
                if (self.crc == ch):
                    return True, msg
                else:
                    print(f'crc error, expect:{self.crc:02x} received:{ch:02x}')

        return False, msg # receive finshed, Msg

    def recv_msg(self):
        receive_finished = False
        while not receive_finished:
            try:
                buf = self.port.read(200)
            except serial.serialutil.SerialException as e:
                # print(e)
                self.error_count += 1
                if(self.error_count == 3):
                    self.signal.serial_error.emit()
                return None
            if len(buf) == 0 and len(self.array) == 0:
                return None, None
            self.array += buf
            raw_buf = self.array # Capture raw_buf here
            # parse received byte array
            receive_finished, self.recvmsg = self.parse(self.recvmsg)
            if receive_finished:
                logger.debug(f"SerialWraper: Received msg_id={self.recvmsg.msg_id:#02x}, payload_start={bytes(self.recvmsg.msg_payload[:5]).hex()}")
                # header_list = [self.SYNC_WORDH, self.SYNC_WORDL, self.recvmsg.msg_id, self.recvmsg.size_high_byte, self.recvmsg.size_low_byte]
                # hex_line = re.sub(r"(?<=\w)(?=(?:\w\w)+$)", " ", bytearray(header_list).hex().upper())
                # hex_line += " "
                # hex_line += (re.sub(r"(?<=\w)(?=(?:\w\w)+$)", " ", bytearray(self.recvmsg.msg_payload).hex().upper()))
                # hex_line += " "
                # hex_line += re.sub(r"(?<=\w)(?=(?:\w\w)+$)", " ", bytearray([self.crc, ]).hex().upper())
                # print(f"{time.time():.3f} <-- recv: {hex_line}")
        return self.recvmsg, raw_buf


    def send_msg(self, msg):
        array = msg.serialize()
        # add protocol wrapper
        packet = Msg(self.SYNC_WORDH, self.SYNC_WORDL)
        packet.msg_id = msg.msg_id
        packet.msg_payload = array
        send_data = packet.serialize()

        try:
            self.port.write(send_data)
            self.port.flush()
        except Exception as e:
            print(e)
        # hex_line = re.sub(r"(?<=\w)(?=(?:\w\w)+$)", " ", send_data.hex().upper())
        # print(f"{time.time():.3f} --> send: {hex_line}, {send_data}, ({msg})")



if __name__ == "__main__":
    import time
    wraper = SerialWraper(serial.Serial("/dev/ttyUSB0",19200,timeout=0.5))
    # send request cmd
    recognize = Recognize()
    wraper.send_msg(recognize)
    # receive response
    start_time = time.time()
    while (time.time() - start_time) < 20:
        msg = wraper.recv_msg()
        get_img = GetRawImgSize()
        wraper.send_msg(get_img)
        wraper.recv_msg()

    print(f'test finish')