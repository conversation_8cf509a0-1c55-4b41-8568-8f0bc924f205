
import json
import re


# indent：缩进， limit：树形展开层级
def jsonIndentLimit(jsonString, indent, limit):
    regexPattern = re.compile(f'\n({indent}){{{limit}}}(({indent})+|(?=(}}|])))')
    return regexPattern.sub('', jsonString)

# JSON文本数据 [] 格式不展开，并去除多余空格
class MyJSONEncoder(json.JSONEncoder):

    def iterencode(self, o, _one_shot=False):
        list_lvl = 0
        for s in super(MyJSONEncoder, self).iterencode(o, _one_shot=_one_shot):
            if s.startswith('['):
                list_lvl += 1
                s = s.replace('\n', '').rstrip()
                if s.find('"') > 0:
                    s = re.sub(" ", "", s, s.find('"') - s.find('[') - 1)
                else:
                    s = s.replace(" ", "")
            elif 0 < list_lvl:
                s = s.replace('\n', '').rstrip()
                if s.find('"') > 0:
                    s = re.sub(" ", "", s, s.find('"') - s.find(',') - 2)
                else:
                    s = s.replace(" ", "")
                    s = s.replace(",", ", ")
                if s and s[-1] == ',':
                    s = s[:-1] + self.item_separator
                elif s and s[-1] == ':':
                    s = s[:-1] + self.key_separator
            if s.endswith(']'):
                list_lvl -= 1
            yield s


def json_text_transform_json_dict(json_cfg_text:str):
    try:
        json_cfg_dict = json.loads(json_cfg_text)
    except Exception as e:
        json_cfg_dict = {}
    return json_cfg_dict


def json_dict_tramsform_json_text(json_cfg_dict:dict):
    try:
        json_cfg_text = json.dumps(json_cfg_dict, indent=2, cls=MyJSONEncoder)
    except Exception as e:
        json_cfg_text = ""
    return json_cfg_text


def json_dict_transformer_json_bytes(json_cfg_dict: dict):
    try:
        json_cfg_str = json.dumps(json_cfg_dict)
        json_bytes = bytes(json_cfg_str, encoding = "utf8")
    except Exception as e:
        json_bytes = bytes()
    return json_bytes


def json_dict_transformer_json_bytes_serialize(json_cfg_dict: dict):
    # separators=(',', ':') effect: remove all space character
    try:
        json_bytes = json.dumps(json_cfg_dict, separators=(',', ':')).encode()
    except Exception as e:
        json_bytes = bytes()
    return json_bytes


def json_bytes_transform_json_dict(json_cfg_bytes:bytes):
    try:
        json_cfg_str = json_cfg_bytes.decode(encoding = "utf8")
        json_cfg_dict = json.loads(json_cfg_str)
    except Exception as e:
        json_cfg_dict = {}
    return json_cfg_dict


def json_bytes_transform_json_text(json_cfg_bytes:bytes):
    json_cfg_dict = json_bytes_transform_json_dict(json_cfg_bytes)
    return json_dict_tramsform_json_text(json_cfg_dict)


def json_text_transform_json_bytes(json_cfg_txt:str):
    json_cfg_dict = json_text_transform_json_dict(json_cfg_txt)
    return json_dict_transformer_json_bytes(json_cfg_dict)