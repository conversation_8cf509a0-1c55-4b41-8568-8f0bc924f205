#! /usr/bin/env python3
# -*- coding: utf-8 -*-
from PyQt5 import QtWidgets, QtCore
from PyQt5.QtCore import Qt, QObject, QTimer, pyqtSignal, pyqtSlot, QTranslator, QSettings
from PyQt5.QtWidgets import QMainWindow, QApplication, QFileDialog, QDialog, QMessageBox
from PyQt5.QtGui import QTextCursor
from qasync import asyncSlot, asyncClose
from ui.OtaWindowUI import Ui_OTAWindowUI
from src.sync_api import HI10
from src.SendFileWindow import SendFile
from src.window_manager import WindowManager
from src.hi10_protocol import *
import os
import math
import hashlib
import time
import asyncio
import enum

DEFAULT_OTA_FILE_PATH = 'ota_file_path'

@enum.unique
class OtaFlag(enum.Enum):
    FILE = 0
    CMD = 1

class OTAWindow(QMainWindow, Ui_OTAWindowUI, WindowManager):
    class UpdateVersionSignal(QObject):
        signal = pyqtSignal()

    # para: 1. hi10 2. serial_port
    def __init__(self, *args, **kwargs) -> None:
        super().__init__()
        WindowManager.__init__(self, *args, **kwargs)
        self.hi10 = args[0]
        self.serial_port = args[1] if (len(args) > 1) else None
        try:
            self.origin_serial_bps = (int)(self.serial_port.baudrate)
        except Exception as e:
            self.origin_serial_bps = 115200
        self.hi10.ota_signal.ota_signal.connect(self.on_notify_ota)
        self.setupUi(self)
        self.file_path = ""
        # self.selectfile_label.setText(self.file_path)
        self.all_func_ui = [
            self.selectfile_btn,
            self.startOTA_btn,
            self.txt_radio_Button,
            self.ota_radio_Button,
            self.json_radio_Button,
            ]
        self.otastatus = ""
        self.cur_packet_id = 0
        self.isPaused = False
        self.isOTAmode = False
        self.is_enable_stop = True
        # self.disable_ui([self.pauseOTA_btn, self.stopOTA_btn])
        self.set_window_position_to_middle(self)
        self.setFixedSize(self.geometry().width(),self.geometry().height())
        self._translate = QtCore.QCoreApplication.translate
        class ConfigSerialSignal(QObject):
            baudrate = pyqtSignal(int)
        self.config_serial_signal = ConfigSerialSignal()
        self.config_serial_status_ok = False
        self.update_version_signal = self.UpdateVersionSignal()
        self.bps_cbx.addItem("460800", "460800")
        self.bps_cbx.addItem("921600", "921600")
        self.bps_cbx.addItem("1500000", "1500000")
        self.bps_cbx.setCurrentIndex(0)
        self.pkt_cbx.addItem("2040", "2040")
        self.pkt_cbx.addItem("4080", "4080")
        self.pkt_cbx.addItem("8160", "8160")
        self.pkt_cbx.setCurrentIndex(0)
        self._load_settings()


    @asyncClose
    async def closeEvent(self, event) -> None:
        if(self.isOTAmode == True or self.isPaused == True):
            self.isOTAmode = False
            while self.is_enable_stop == False:
                await asyncio.sleep(0.01)
            await self._wrapper_send_OTA_msg(self.hi10.send_ota_stop, 1)
            self.config_serial_signal.baudrate.emit(0)
        self.close_window_signal.signal.emit()

    def _load_settings(self) -> None:
        """Load settings on startup."""
        settings = QSettings('AIVA', 'FaceLockApp')
        # port name
        self.file_path = settings.value(DEFAULT_OTA_FILE_PATH)
        bps = settings.value("ota_bps")
        if bps is not None:
            index = self.bps_cbx.findData(bps)
            self.bps_cbx.setCurrentIndex(index if index > -1 else 0)
        pkt = settings.value("ota_pkt")
        if pkt is not None:
            index = self.pkt_cbx.findData(pkt)
            self.pkt_cbx.setCurrentIndex(index if index > -1 else 0)

    def _save_settings(self) -> None:
        """Save settings on shutdown."""
        settings = QSettings('AIVA', 'FaceLockApp')
        settings.setValue(DEFAULT_OTA_FILE_PATH, self.file_path)
        settings.setValue("ota_bps", self.bps_cbx.currentText())
        settings.setValue("ota_pkt", self.pkt_cbx.currentText())
        settings.sync()

    def on_selectfile_btn_pressed(self) -> None:
        pattern = '*.txt' if self.txt_radio_Button.isChecked() else '*.ota' if self.ota_radio_Button.isChecked() else 'facelock_cfg.json'
        file_name = QFileDialog.getOpenFileName(self, "Open File", self.file_path, f"{pattern} ({pattern})")
        self.file_path = file_name[0]
        # self.selectfile_label.setText(self.file_path)
        # self.selectfile_label.adjustSize()
        # self.selectfile_label.setAlignment(Qt.AlignTop)
        # self.selectfile_label.setWordWrap(True)
        self.otastatus = self._translate('OTAWindow', 'choose firmware file:\n') + self.file_path
        self.otastatus_textEdit.setText(self.otastatus)

    def ota_status_refresh(self):
        self.otastatus_textEdit.append(self.otastatus)
        cursor = self.otastatus_textEdit.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.otastatus_textEdit.setTextCursor(cursor)

    def refresh_OTA_pkt_replay_status(self, func, result) -> None:
        if func == self.hi10.send_ota_start:
            self.otastatus = self._translate('OTAWindow','successfully enter upgrade mode')
        elif func == self.hi10.send_ota_get_status:
            self.otastatus = self._translate('OTAWindow', 'get the status = ') + str(result.otaStatus) + '\n'
            self.otastatus += self._translate('OTAWindow', 'next pid = ') + str(result.nextExpectedPID)
            self.next_packet_id = result.nextExpectedPID
            self.cur_packet_id = self.next_packet_id - 1
        elif func == self.hi10.send_ota_header:
            self.otastatus = self._translate('OTAWindow', 'successfully send OTA header')
        elif func == self.hi10.send_ota_packet:
            self.otastatus = self._translate('OTAWindow', 'successfully send OTA data, pid = ') + str(self.cur_packet_id)
            pass
        elif func == self.hi10.send_ota_stop:
            self.otastatus = self._translate('OTAWindow', 'successfully stop OTA')
        self.ota_status_refresh()

    async def _wrapper_send_OTA_msg(self, func, *args) -> bool:
        self.is_enable_stop = False
        ret_code, result = await func(*args)
        self.is_enable_stop = True
        if ret_code != HI10.ReturnCode.SUCCESS:
            print('error!!!')
            return False
        self.refresh_OTA_pkt_replay_status(func, result)
        return True

    def checkret(self, ret) -> bool:
        if(ret == False):
            self.enable_ui(self.all_func_ui)
            # self.disable_ui([self.pauseOTA_btn,self.stopOTA_btn,])
            return False
        if(self.isOTAmode == False):
            # self.enable_ui([self.stopOTA_btn,])
            return False
        return True

    @pyqtSlot()
    def on_startOTA_btn_pressed(self):
        self._save_settings()
        async def _start_upgrade():
            return await self.__upload_file_for_upgrade()
        f = asyncio.ensure_future(_start_upgrade())
        while not f.done():
            QApplication.instance().processEvents()
        if f.result():
            self.update_version_signal.signal.emit()


    async def __get_ready(self, hi10):
        try:
            ret_code, result = await hi10.get_module_status(timeout_second = 2)
        except Exception as e:
            return False, "get_module_status exception"
        if ret_code == HI10.ReturnCode.SUCCESS:
            assert isinstance(result, Reply.ReplyGetStatus)
            if result.status == Reply.ReplyGetStatus.ModuleState.MS_STANDBY:
                return True, ""
            elif result.status == Reply.ReplyGetStatus.ModuleState.MS_BUSY:
                return False, '设备忙，请等待上个操作完成'
            elif result.status == Reply.ReplyGetStatus.ModuleState.MS_ERROR:
                return True, '镜头标定参数检查未通过'
            else:
                module_state = Reply.ReplyGetStatus.ModuleState.module_state_str(result.status)
                return False, module_state
        else:
            return False, '串口错误! 请检查是否正确连接,是否已经上电'


    @asyncSlot()
    async def __upload_file_for_upgrade(self):
        update_file_name = "firmware_update.txt"
        path = self.file_path.rsplit('/', maxsplit=1)
        path = path[-1]
        burn_result = False
        bps = (int)(self.bps_cbx.currentText())
        pkt = (int)(self.pkt_cbx.currentText())
        if self.txt_radio_Button.isChecked():
            if path != update_file_name:
                self.show_error_message(self._translate('OTAWindow','please choose txt file named firmware_update.txt'))
                return False
            else:
                files_update_cnt, files_need_update = self.parsing_batched_load_files(self.file_path)
                if files_update_cnt != 0:
                    # await self.baudrate_config(460800)
                    file_cnt = 0
                    percentage = 100 / files_update_cnt
                    for item in files_need_update:
                        file_cnt += 1
                        start_percent = (file_cnt-1) * percentage
                        end_percent = file_cnt * percentage
                        item_type = item[0]
                        file_name = item[1]
                        item_flag = item[2]
                        if item_type == 'OTA' or item_type == 'ota':
                            burn_result = await self.update_OTA_file(file_name, int(start_percent), int(end_percent), bps, pkt)
                            if burn_result == False:
                                break
                        elif item_type == 'FILE' or item_type == 'file':
                            if "facelock_cfg.json" in file_name:
                                burn_result = await self.upload_json_cfg(file_name, int(end_percent))
                            else:
                                burn_result = await self.update_normal_file(file_name, int(end_percent))
                            if burn_result == False:
                                break
                        elif item_type == 'ota_jump' or item_type == 'OTA_JUMP':
                            ret_code, result = await self.hi10.ota_jump(enable = True, timeout_second = 1)
                            if ret_code == HI10.ReturnCode.SUCCESS:
                                self.otastatus = f"ota_jump: {result[2]}, {ret_code}"
                                self.ota_status_refresh()
                            else:
                                self.show_error_message(f"ota_jump: {result[2]}, {ret_code}")
                                break
                        elif item_type == 'no_ota_jump' or item_type == 'NO_OTA_JUMP':
                            ret_code, result = await self.hi10.ota_jump(enable = False, timeout_second = 1)
                            if ret_code == HI10.ReturnCode.SUCCESS:
                                self.otastatus = f"no ota_jump: {result[2]}, {ret_code}"
                                self.ota_status_refresh()
                            else:
                                self.show_error_message(f"ota_jump: {result[2]}, {ret_code}")
                                break
                        elif item_type == 'reboot' or item_type == 'REBOOT':
                            cmd_str = "reboot"
                            # 发送reboot命令，不等待响应（因为设备会重启）
                            ret, result = await self.hi10.send_cmd_parse(0x00, len(cmd_str), cmd_str, 0)
                            if ret == HI10.ReturnCode.SUCCESS:
                                self.otastatus = "reboot: command sent, device restarting..."
                                self.ota_status_refresh()
                                # 等待一段时间让设备重启
                                await asyncio.sleep(3)
                            else:
                                self.show_error_message(f"reboot: failed to send command, ota error, please try upgrade again")
                                break
                        else:
                            print('error!')
                            break
        elif self.ota_radio_Button.isChecked():
            if self.file_path[-4:] != '.ota':
                self.show_error_message(self._translate('OTAWindow','error ota file'))
                return False
            else:
                burn_result = await self.update_OTA_file(self.file_path, 0, 100, bps, pkt)
        elif self.json_radio_Button.isChecked():
            # burn_result = await self.send_facelock_cfg_file(self.file_path)
            # burn_result = await self.update_normal_file(self.file_path, 100)
            burn_result = await self.upload_json_cfg(self.file_path, 100)
            # with open(self.file_path, 'r') as f:
            #     new_cfg = bytearray(''.join(f.readlines()), 'ascii')
            #     ret_code, result = await self.hi10.get_logfile(LogFileType.LOG_FILE_KERNEL, 5)
            #     burn_result = ret_code == HI10.ReturnCode.SUCCESS and result == new_cfg
        if burn_result:
            self.show_info_message(self._translate('OTAWindow', 'OTA update successfully, please restart firmware!'))
        else:
            self.show_error_message(self._translate('OTAWindow', 'OTA update failed, please retry!'))
            # await self.baudrate_config(115200)
        return burn_result

    def parsing_batched_load_files(self, batch_file) -> bool:
        valid_file_cnt = 0
        ota_items = []# [ ("type": "", "file_name": "", "flag": OtaFlag.FILE)]
        if not batch_file:
            return (valid_file_cnt, ota_items)
        #
        paths = batch_file.rsplit('/', maxsplit=1)
        fold_path = paths[0]

        print(__file__ + "\n")
        with open(batch_file,'r') as file_obj:
            for line in file_obj:
                line = line.strip()
                if not line or line[0] == '#' or line == '\n':
                    continue
                file_line = line.split()
                if len(file_line) > 0:
                    type = file_line[0]
                    if len(file_line) == 2:
                        file_name = file_line[1]
                    else:
                        file_name = ""
                    if type == 'OTA' or type == 'ota':
                        if file_name[-4:] != '.ota':
                            self.otastatus = self._translate('OTAWindow','ota file name is not valid')
                            self.ota_status_refresh()
                            continue
                        else:
                            valid_file_cnt += 1
                            file_name = fold_path + '/' + file_name
                            ota_items.append((type, file_name, OtaFlag.FILE))
                    elif type == 'FILE' or type == 'file':
                        valid_file_cnt += 1
                        file_name = fold_path + '/' + file_name
                        ota_items.append((type, file_name, OtaFlag.FILE))
                    elif type == 'ota_jump' or type == 'OTA_JUMP':
                        valid_file_cnt += 1
                        ota_items.append((type, "", OtaFlag.CMD))
                    elif type == 'reboot' or type == 'REBOOT':
                        valid_file_cnt += 1
                        ota_items.append((type, "", OtaFlag.CMD))
                    elif type == 'no_ota_jump' or type == 'NO_OTA_JUMP':
                        valid_file_cnt += 1
                        ota_items.append((type, "", OtaFlag.CMD))
                    else:
                        self.otastatus = self._translate('OTAWindow','unrecognized file type')
                        self.ota_status_refresh()
                        continue
                else:
                    # print('format is not valid')
                    self.otastatus = self._translate('OTAWindow','format is not valid!')
                    self.ota_status_refresh()
                    continue
        return (valid_file_cnt, ota_items)

    @asyncSlot()
    async def baudrate_config(self, baudrate, timeout = 3) -> bool:
        ret_code, result = await self.hi10.config_baudrate(baudrate, timeout_second=5)
        if ret_code == HI10.ReturnCode.SUCCESS:
            self.config_serial_signal.baudrate.emit(baudrate)
            self.otastatus = self._translate('OTAWindow','successfully set baudrate') + f'{baudrate}'
            self.ota_status_refresh()
            return True
        else:
            self.otastatus = self._translate('OTAWindow','failed to set baudrate') + f'{baudrate}'
            self.ota_status_refresh()
            return False

    @asyncSlot()
    async def update_OTA_file(self, ota_file_name: str, start_percent, end_percent: float, bps: int = 460800, pkt: int = 2040):
        if(os.path.isfile(ota_file_name) == False):
            self.otastatus = self._translate('OTAWindow', 'file name match error!')
            self.ota_status_refresh()
            self.show_error_message('OTAWindow, file name match error!')
            return False
        #
        timeout = 3
        #
        self.isOTAmode = True
        self.is_notify_recv = False
        self.disable_ui(self.all_func_ui)
        # self.enable_ui([self.pauseOTA_btn,self.stopOTA_btn,])
        #
        if(self.isPaused == False):
            ret = await self._wrapper_send_OTA_msg(self.hi10.send_ota_start, timeout)
            if(self.checkret(ret) == False):
                return False
            await self.baudrate_config(bps)
        await asyncio.sleep(0.1)
        ret = await self._wrapper_send_OTA_msg(self.hi10.send_ota_get_status, timeout)
        if(self.checkret(ret) == False):
            return False

        size_per_pkt = pkt
        if(self.isPaused == False):
            file_size = os.path.getsize(ota_file_name)
            print(f'file size is {file_size:d}')
            self.pkt_number = math.ceil(file_size / size_per_pkt)
            print(self.pkt_number)
            with open(ota_file_name, 'rb') as file:
                hashcode = hashlib.md5(file.read()).hexdigest()
            ret = await self._wrapper_send_OTA_msg(self.hi10.send_ota_header, file_size, size_per_pkt, self.pkt_number, hashcode, timeout)
            if(self.checkret(ret) == False):
                return False

        packet_id = self.cur_packet_id
        n = 0
        self.otastatus = self._translate('OTAWindow','start transfering file, please wait......')
        self.ota_status_refresh()
        sendfile = SendFile(self.hi10)
        for data in sendfile.get_file_data(ota_file_name, size_per_pkt):
            if(n < self.next_packet_id - 1):
                n += 1
                continue
            packet_id += 1
            n = packet_id
            self.cur_packet_id = packet_id
            # print(f'packet id is {packet_id:d}')
            packet_size = len(data)
            ret = await self._wrapper_send_OTA_msg(self.hi10.send_ota_packet, packet_id, packet_size, data, timeout)
            if(ret == False):
                if(self.cur_packet_id > 0):
                    self.cur_packet_id -= 1
                self.enable_ui(self.all_func_ui)
                # self.disable_ui([self.pauseOTA_btn,self.stopOTA_btn,])
                return False
            percent = start_percent + (int)((packet_id / self.pkt_number * 95) * (end_percent - start_percent) / 100)
            self.ota_progressBar.setValue(percent)
            if(self.isOTAmode == False):
                return False
        self.otastatus = self._translate('OTAWindow','file data transfer over.\n')
        self.otastatus += self._translate('OTAWindow','waiting for firmware burning flash......')
        self.ota_status_refresh()
        await_is_notify_recv_cnt = 0
        while self.is_notify_recv == False:
            await asyncio.sleep(1)
            await_is_notify_recv_cnt += 1
            if await_is_notify_recv_cnt > 300:
                return False
        timeout = 1
        ret = await self._wrapper_send_OTA_msg(self.hi10.send_ota_stop, timeout)
        if(self.checkret(ret) == False):
            return False
        
        is_success = False
        if(self.upgrade_status == 0):
            self.otastatus = self._translate('OTAWindow','successfully upgrade firmware!')
            self.ota_status_refresh()
            self.ota_progressBar.setValue(end_percent)
            is_success = True
        else:
            self.otastatus = self._translate('OTAWindow','failed to upgrade firmware!')
            self.ota_status_refresh()
            is_success = False
            await self.baudrate_config(19200)
        self.isPaused = False
        self.isOTAmode = False
        self.enable_ui(self.all_func_ui)
        # self.disable_ui([self.pauseOTA_btn, self.stopOTA_btn])
        #
        self.config_serial_signal.baudrate.emit(0)
        await asyncio.sleep(1)
        return is_success


    async def upload_json_cfg(self, file_path: str, percentage: int):
        new_cfg = None
        if os.path.isfile(file_path):
            with open(file_path, 'r') as f:
                json_data_text = f.read()
                json_cfg_bytes = json_text_transform_json_bytes(json_data_text)
                new_cfg = json_cfg_bytes
        return await self.upload_json_cfg_bytes(new_cfg, percentage)


    async def upload_json_cfg_bytes(self, json_cfg_bytes: bytes, percentage: int):
        if json_cfg_bytes is None or len(json_cfg_bytes) == 0:
            self.otastatus = self._translate('OTAWindow', 'read json cfg data error!')
            self.ota_status_refresh()
            self.show_error_message(self._translate('OTAWindow read json cfg data error!'))
            return False
        total_packet_size = len(json_cfg_bytes)
        json_dict = json_bytes_transform_json_dict(json_cfg_bytes)
        cfg_baudrate = 115200
        if json_dict.__contains__("common"):
            if json_dict["common"].__contains__("serial_baudrate"):
                cfg_baudrate = json_dict["common"]["serial_baudrate"]
        try :
            offset = 0
            total_pkt_number = math.ceil(total_packet_size / 1024)
            current_pkt_num = 0
            while current_pkt_num < total_pkt_number:
                current_pkt_num += 1
                if total_pkt_number == 1:
                    data = json_cfg_bytes[:]
                elif current_pkt_num == total_pkt_number:
                    data = json_cfg_bytes[offset : ]
                else:
                    data = json_cfg_bytes[offset : offset + 1024]
                packet_size = len(data)
                ret_code, result = await self.hi10.send_file_data(StoreType.TYPE_MEM, total_packet_size, offset, packet_size, data, timeout_second = 10)
                if ret_code != HI10.ReturnCode.SUCCESS:
                    print('send json cfg data error!')
                    self.otastatus = self._translate('OTAWindow','send json cfg data error!\n')
                    self.ota_status_refresh()
                    return False
                else:
                    offset += packet_size
                    self.otastatus = self._translate('OTAWindow', 'successfully send json cfg data, pid = ') + str(current_pkt_num)
                    self.ota_status_refresh()
            # send file end packet -> offset=0 and packet_size=0
            print(f'send end packet')
            data = bytearray(0)
            ret_code, result = await self.hi10.send_file_data(StoreType.TYPE_MEM, total_packet_size, 0, 0, data, timeout_second=5)
            if ret_code != HI10.ReturnCode.SUCCESS:
                print('send end packet error!')
                self.otastatus = self._translate('OTAWindow','send end packet error!\n')
                self.ota_status_refresh()
                return False
            else:
                self.otastatus = self._translate('OTAWindow','successfully send end packet!\n')
                self.ota_status_refresh()
            ret_code, result = await self.hi10.send_file_info("facelock_cfg.json", timeout_second = 3)
            if ret_code == HI10.ReturnCode.SUCCESS:
                self.otastatus = self._translate('OTAWindow','successfully write file to fs .\n')
                self.ota_status_refresh()
            else:
                self.otastatus = self._translate('OTAWindow','failed write file to fs.\n')
                self.ota_status_refresh()
                return False
            if (self.origin_serial_bps != cfg_baudrate):
                await asyncio.sleep(0.4) # wait send_cmd_parse done
                self.config_serial_signal.baudrate.emit(cfg_baudrate)
                await asyncio.sleep(0.1) # wait baudrate change done
                self.origin_serial_bps = cfg_baudrate
            if glva.g_enable_jump_fw1_with_acm == True:
                return True
            # 发送reboot命令，不等待响应（因为设备会重启）
            ret_code, result = await self.hi10.send_cmd_parse(0x00, len("reboot"), "reboot", 0)
            if ret_code == HI10.ReturnCode.SUCCESS:
                self.otastatus = "reboot: command sent, device restarting..."
                self.ota_status_refresh()
                # 等待一段时间让设备重启
                await asyncio.sleep(3)
            else:
                self.show_error_message(f"reboot: failed to send command, ota error, please try upgrade again")
                return False
            ret_code, result = await self.hi10.get_logfile(LogFileType.LOG_FILE_KERNEL, 5)
            if result == json_cfg_bytes:
                self.otastatus = self._translate('OTAWindow','successfully compare upload and download json cfg\n')
                self.ota_status_refresh()
                self.ota_progressBar.setValue(percentage)
                return True
            else:
                self.otastatus = self._translate('OTAWindow','failed compare upload and download json cfg\n')
                self.ota_status_refresh()
                return False
        except Exception as e:
            self.otastatus = self._translate('OTAWindow','upload facelock_cfg.json exception!\n')
            self.ota_status_refresh()
        return False


    @asyncSlot()
    async def update_normal_file(self, file_name: str, percentage: int):
        if(os.path.isfile(file_name) == False):
            self.otastatus = self._translate('OTAWindow', 'file name match error!')
            self.ota_status_refresh()
            self.show_error_message('OTAWindow file name match error!')
            return False
        #
        offset = 0
        pid = 0
        file_size = os.path.getsize(file_name)
        with open(file_name, 'rb'):
            sendfile = SendFile(self.hi10)
            for data in sendfile.get_file_data(file_name):
                packet_size = len(data)
                pid += 1
                print(f'packet id is {pid}')
                ret_code, result = await self.hi10.send_file_data(StoreType.TYPE_MEM, file_size, offset, packet_size, data, timeout_second=5)
                if ret_code != HI10.ReturnCode.SUCCESS:
                    print('send file error!')
                    self.otastatus = self._translate('OTAWindow','send file error!\n')
                    self.ota_status_refresh()
                    return False
                else:
                    offset += packet_size
                    self.otastatus = self._translate('OTAWindow', 'successfully send data, pid = ') + str(pid)
                    self.ota_status_refresh()

        # send file end packet -> offset=0 and packet_size=0
        print(f'send end packet')
        data = bytearray(0)
        ret_code, result = await self.hi10.send_file_data(StoreType.TYPE_MEM, file_size, 0, 0, data, timeout_second=5)
        if ret_code != HI10.ReturnCode.SUCCESS:
            print('send file error!')
            self.otastatus = self._translate('OTAWindow','send file error!\n')
            return False
        else:
            self.otastatus = self._translate('OTAWindow','successfully send end packet!\n')
        #write file to fs
        file_name = file_name.rsplit('/', 1)
        file_name = file_name[1]
        ret_code, result = await self.hi10.send_file_info(file_name, timeout_second = 3)
        if ret_code == HI10.ReturnCode.SUCCESS:
            self.otastatus = self._translate('OTAWindow','successfully write file to fs .\n')
            self.ota_progressBar.setValue(percentage)
        else:
            self.otastatus = self._translate('OTAWindow','failed write file to fs.\n')
        self.ota_status_refresh()
        return True

    # @asyncSlot()
    # async def on_pauseOTA_btn_pressed(self) -> None:
    #     self.isPaused = True
    #     self.isOTAmode = False
    #     while self.is_enable_stop == False:
    #         await asyncio.sleep(0.01)
    #     self.disable_ui([self.pauseOTA_btn,self.selectfile_btn])
    #     self.enable_ui([self.startOTA_btn, self.stopOTA_btn])
    #     self.otastatus = self._translate('OTAWindow','pause upgrade firmware!')
    #     self.ota_status_refresh()

    # @asyncSlot()
    # async def on_stopOTA_btn_pressed(self) -> None:
    #     timeout = 1
    #     self.isOTAmode  = False
    #     self.isPaused = False
    #     self.disable_ui([self.stopOTA_btn,self.pauseOTA_btn])
    #     while self.is_enable_stop == False:
    #         await asyncio.sleep(0.01)
    #     ret = await self._wrapper_send_OTA_msg(self.hi10.send_ota_stop, timeout)
    #     if(ret == False):
    #         return
    #     self.enable_ui([self.selectfile_btn, self.startOTA_btn])
    #     self.otastatus = self._translate('OTAWindow','stop upgrade firmware!')
    #     self.ota_status_refresh()
    #     self.cur_packet_id = 0
    #     #
    #     self.config_serial_signal.baudrate.emit(0)

    @pyqtSlot(int)
    def on_notify_ota(self, result: int) -> None:
        self.is_notify_recv = True
        self.upgrade_status = result

    def show_error_message(self, msg: str) -> None:
        """Show a Message Box with the error message."""
        QMessageBox.critical(self, QApplication.applicationName(), str(msg))
    
    def show_info_message(self, msg: str) -> None:
        """Show a Message Box with the error message."""
        QMessageBox.information(self, QApplication.applicationName(), str(msg))