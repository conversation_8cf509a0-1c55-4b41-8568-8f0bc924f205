#! /usr/bin/env python3
# -*- coding: utf-8 -*-

from .async_serial import <PERSON><PERSON><PERSON><PERSON><PERSON>, SerialWriter
from .hi10_protocol import *
from PyQt5.QtCore import QObject, pyqtSignal
import asyncio
import threading
import logging
from PyQt5.QtCore import Qt

logger = logging.getLogger(__name__)

class HI10():
    def __init__(self, serial_wrapper):
        self.serial_wrapper = serial_wrapper
        self.reader = SerialReader(serial_wrapper)
        self.reader.signals.result.connect(lambda msg, raw_data: self.msg_handler(msg, raw_data), Qt.BlockingQueuedConnection)
        self.writer = SerialWriter(serial_wrapper)
        self.running_cmds = {} # msg_id future map
        self.cmds_lock = threading.Lock()
        class RcvDataSignal(QObject):
            signal = pyqtSignal(int, int, name='receiveDataProcess')
        self.rcv_data_signal = RcvDataSignal()
        class NotifySignals(QObject):
            face_state_signal = pyqtSignal(tuple, int, int, int, name='notifyFaceStateSignal')
            ota_signal = pyqtSignal(int, name='notifyOTASignal')
        self.notify_signal = NotifySignals()
        self.ota_signal = NotifySignals()
        self._translate = QtCore.QCoreApplication.translate
        self.verify_error_reply_msg = None

    def connect(self):
        # start reader and writer thread
        self.reader.start()
        self.writer.start()

    def disconnect(self):
        self.writer.stop()
        self.writer.wait()
        self.reader.stop()
        self.reader.wait()

    def change_baudrate(self, baudrate):
        self.disconnect()
        self.serial_wrapper.change_baudrate(baudrate)
        self.connect()

    class ReturnCode:
        SUCCESS = 0
        INFO    = 1
        ERROR   = 2
        WARNING = 3

    class SpecialCmd(object):
        class CmdID:
            WAIT_READY = -1

        def __init__(self, id) -> None:
            self.msg_id = id

    async def reset(self, timeout_second) -> tuple:
        msg = Reset()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            if isinstance(result, Reply):
                if result.return_code == 0:
                    return (self.ReturnCode.SUCCESS, Reply.get_reply_result(result.return_code))
                else:
                    return (self.ReturnCode.ERROR, Reply.get_reply_result(result.return_code))
            else:
                assert False, f'should not come here'
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','reset timeout')))

    async def wait_ready(self, timeout_second) -> tuple:
        msg = self.SpecialCmd(self.SpecialCmd.CmdID.WAIT_READY)
        result = await self._commit_special_msg(msg, timeout_second)
        if result is not None:
            if isinstance(result, Notify):
                return (self.ReturnCode.SUCCESS, Reply.get_reply_result(self.ReturnCode.SUCCESS))
            else:
                assert False, f'should not come here'
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','wait_ready timeout')))

    async def get_module_status(self, timeout_second) -> tuple:
        msg = GetStatus()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            if isinstance(result, Reply):
                return (self.ReturnCode.ERROR, Reply.get_reply_result(result.return_code))
            elif isinstance(result, Reply.ReplyGetStatus):
                return (self.ReturnCode.SUCCESS, result)
            else:
                assert False, f'should not come here'
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','get_module_status timeout')))

    async def read_alg_image(self, timeout_second) -> tuple:
        ret_code, result = await self._get_alg_imgsize(timeout_second=5)
        if ret_code == self.ReturnCode.SUCCESS:
            assert isinstance(result, Reply.ReplyDataSize)
            ret_code, image = await self._get_data(result.size, msg_id=MsgID.MID_UPLOAD_ALG_IMAGE[0])
            if ret_code == self.ReturnCode.SUCCESS:
                return (self.ReturnCode.SUCCESS, image)
            else:
                return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api', 'read_img failed')))
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api', 'get_snaped_imgsize failed')))

    async def read_raw_image(self, timeout_second) -> tuple:
        ret_code, result = await self._get_raw_imgsize(timeout_second=5)
        if ret_code == self.ReturnCode.SUCCESS:
            assert isinstance(result, Reply.ReplyDataSize)
            ret_code, image = await self._get_data(result.size, msg_id=MsgID.MID_UPLOAD_RAW_IMAGE[0])
            if ret_code == self.ReturnCode.SUCCESS:
                return (self.ReturnCode.SUCCESS, image)
            else:
                return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api', 'read_img failed')))
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api', 'get_snaped_imgsize failed')))

    async def _get_raw_imgsize(self, timeout_second) -> tuple:
        msg = GetRawImageSize()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            if isinstance(result, Reply):
                return (self.ReturnCode.ERROR, Reply.get_reply_result(result.return_code))
            else:
                return (self.ReturnCode.SUCCESS, result)
        else:
            return (self.ReturnCode.ERROR, (0, 0, '_get_raw_imgsize timeout'))

    async def _get_alg_imgsize(self, timeout_second) -> tuple:
        msg = GetAlgImageSize()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            if isinstance(result, Reply):
                return (self.ReturnCode.ERROR, Reply.get_reply_result(result.return_code))
            else:
                return (self.ReturnCode.SUCCESS, result)
        else:
            return (self.ReturnCode.ERROR, (0, 0, '_get_alg_imgsize timeout'))

    async def __get_data(self, offset, size, msg_id, timeout_second) -> tuple:
        max_retry_cnt = 2
        for i in range(max_retry_cnt):
            msg = None
            if msg_id == MsgID.MID_UPLOAD_RAW_IMAGE[0]:
                msg = UploadRawImage(offset, size)
            elif msg_id == MsgID.MID_UPLOAD_ALG_IMAGE[0]:
                msg = UploadAlgImage(offset, size)
            elif msg_id == MsgID.MID_UPLOAD_LOGFILE[0]:
                msg = UploadLogfile(offset, size)
            else:
                return (self.ReturnCode.ERROR, (0, 0, 'invalid msg_id for __get_data'))

            result = await self._commit_msg(msg, timeout_second)
            print(f'__get_data: offset={offset}, size={size}, msg_id={msg_id}, result={result}')
            if result is None and (i < max_retry_cnt - 1):
                print(f'__get_data failed, try again')
                continue

            if result is not None:
                if isinstance(result, Reply):
                    logger.debug(f'__get_data: received Reply with return_code={result.return_code}')
                    return (self.ReturnCode.ERROR, Reply.get_reply_result(result.return_code))
                else:
                    logger.debug(f'__get_data: received data, type={type(result)}')
                    return (self.ReturnCode.SUCCESS, result)
            else:
                return (self.ReturnCode.ERROR, (0, 0, 'get_data timeout'))

    async def _get_data(self, data_size, msg_id) -> tuple:
        data = bytearray(0)
        remain_size = data_size
        offset = 0
        while remain_size > 0:
            # 4000 is the maxmum read len according to protocol
            # be carefull in encrypt mode length should be multiple of 32
            read_size = min(remain_size, 4000)
            ret, partial_data = await self.__get_data(offset, read_size, msg_id, timeout_second = 5)
            if ret == self.ReturnCode.SUCCESS:
                if len(partial_data.uploaded_data) > read_size:
                    data += bytearray(partial_data.uploaded_data[:read_size])
                else:
                    data += bytearray(partial_data.uploaded_data)
                offset += read_size
                remain_size -= read_size
                #
                self.rcv_data_signal.signal.emit(len(data), data_size)
            else:
                return (self.ReturnCode.ERROR, (0, 0, 'get_data failed'))

        return (self.ReturnCode.SUCCESS, data)


    async def _get_logfile_size(self, timeout_second) -> tuple:
        msg = GetLogfileSize()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            if isinstance(result, Reply):
                return (self.ReturnCode.ERROR, Reply.get_reply_result(result.return_code))
            else:
                return (self.ReturnCode.SUCCESS, result)
        else:
            return (self.ReturnCode.ERROR, (0, 0, '_get_logfile_size timeout'))
    async def get_logfile(self, timeout_second) -> tuple:
        ret_code, result = await self._get_logfile_size(timeout_second=5)
        if ret_code == self.ReturnCode.SUCCESS:
            assert isinstance(result, Reply.ReplyDataSize)
            ret_code, data = await self._get_data(result.size, msg_id=MsgID.MID_UPLOAD_LOGFILE[0])
            if ret_code == self.ReturnCode.SUCCESS:
                return (self.ReturnCode.SUCCESS, data)
            else:
                return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api', 'get_logfile failed')))
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api', 'get_logfile failed')))

    async def label_recog(self, timeout_second) -> tuple:
        msg = LabelRecog(timeout_second)
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            if isinstance(result, Reply):
                self.verify_error_reply_msg = result.msg_payload
                return (self.ReturnCode.ERROR, Reply.get_reply_result(result.return_code))
            else:
                assert isinstance(result, Reply.ReplyRecognize)
                return (self.ReturnCode.SUCCESS, (0, 0, self._translate('sync_api','temperature_level') + f':{result.temperature_level}' + ' ' + self._translate('sync_api','with cloth background') + f':{result.with_under_cloth}'))
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','verify timeout')))

    async def config_baudrate(self, baudrate, timeout_second):
        msg = ConfigBaudrate(baudrate)
        # result = Reply subclass .parse()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            assert isinstance(result, Reply), f'return of speed negotiation must be instance Reply'
            ret_code = self.ReturnCode.SUCCESS if result.return_code == 0 else self.ReturnCode.ERROR
            await asyncio.sleep(0.1)
            return (ret_code, Reply.get_reply_result(result.return_code))
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','config baudrate timeout')))

    async def send_ota_start(self, timeout_second):
        msg = StartOTA()
        # result = Reply subclass .parse()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            assert isinstance(result, Reply), f'return of start ota must be instance Reply'
            ret_code = self.ReturnCode.SUCCESS if result.return_code == 0 else self.ReturnCode.ERROR
            return (ret_code, Reply.get_reply_result(result.return_code))
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','start_ota timeout')))

    async def send_ota_get_status(self, timeout_second):
        msg = GetOTAStatus()
        # result = Reply subclass .parse()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            if isinstance(result, Reply):
                return (self.ReturnCode.INFO, Reply.get_reply_result(result.return_code))
            else:
                return (self.ReturnCode.SUCCESS, result)
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','get_ota_status timeout')))

    async def send_ota_header(self, file_size, size_per_pkt, pkt_number, hashcode, timeout_second):
        msg = SendOTAHeader(file_size, size_per_pkt, pkt_number, hashcode)
        # result = Reply subclass .parse()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            assert isinstance(result, Reply), f'return of send ota header must be instance Reply'
            ret_code = self.ReturnCode.SUCCESS if result.return_code == 0 else self.ReturnCode.ERROR
            return (ret_code, Reply.get_reply_result(result.return_code))
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','send_ota_header timeout')))

    async def send_ota_packet(self, packet_id, packet_size, data, timeout_second):
        msg = SendOTAPacket(packet_id, packet_size, data)
        # result = Reply subclass .parse()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            assert isinstance(result, Reply), f'return of send ota packet must be instance Reply'
            ret_code = self.ReturnCode.SUCCESS if result.return_code == 0 else self.ReturnCode.ERROR
            return (ret_code, Reply.get_reply_result(result.return_code))
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','send_ota_packet timeout')))

    async def send_ota_stop(self, timeout_second):
        msg = StopOTA()
        # result = Reply subclass .parse()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            assert isinstance(result, Reply), f'return of stop ota must be instance Reply'
            ret_code = self.ReturnCode.SUCCESS if result.return_code == 0 else self.ReturnCode.ERROR
            return (ret_code, Reply.get_reply_result(result.return_code))
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','stop_ota timeout')))

    async def send_file_data(self, store_type, file_size, offset, packet_size, data, timeout_second):
        msg = SendFilePacket(store_type, file_size, offset, packet_size, data)
        # result = Reply subclass .parse()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            assert isinstance(result, Reply), f'return of send file must be instance Reply'
            ret_code = self.ReturnCode.SUCCESS if result.return_code == 0 else self.ReturnCode.ERROR
            return (ret_code, Reply.get_reply_result(result.return_code))
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','send file timeout')))
    
    async def get_firmware_version(self, timeout_second):
        msg = GetFWVersion()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            if isinstance(result, Reply):
                return (self.ReturnCode.INFO, Reply.get_reply_result(result.return_code))
            else:
                return (self.ReturnCode.SUCCESS, result)
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','get firmware version timeout')))

    async def get_lib_model_version(self, timeout_second):
        msg = GetLibModelVersion()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            if isinstance(result, Reply):
                return (self.ReturnCode.INFO, Reply.get_reply_result(result.return_code))
            else:
                return (self.ReturnCode.SUCCESS, result)
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','get lib version timeout')))

    async def get_board_serial_number(self, timeout_second):
        msg = GetSNInfo()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            if isinstance(result, Reply):
                return (self.ReturnCode.INFO, Reply.get_reply_result(result.return_code))
            else:
                return (self.ReturnCode.SUCCESS, result)
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','get board sn timeout')))

    async def send_file_info(self, file_name: str, timeout_second) -> None:
        msg = FileInfo(file_name)
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            assert isinstance(result, Reply), f'return of send file info must be instance Reply'
            ret_code = self.ReturnCode.SUCCESS if result.return_code == 0 else self.ReturnCode.ERROR
            return (ret_code, Reply.get_reply_result(result.return_code))
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','send file info timeout')))
    

    async def send_cmd_parse(self, cmd_code, cmd_len, data, timeout_second) -> None:
        msg = CmdParse.from_seperate_params(cmd_code, cmd_len, data)
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            if isinstance(result, Reply):
                ret_code = self.ReturnCode.SUCCESS if result.return_code == 0 else self.ReturnCode.ERROR
                return (ret_code, Reply.get_reply_result(result.return_code))
            else:
                assert isinstance(result, Reply.ReplyCmdParse), f'return of cmd parse must be instance ReplyCmdParse'
                return (self.ReturnCode.SUCCESS, result)
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','cmd parse timeout')))

    async def power_down(self, timeout_second) -> None:
        msg = PowerDown()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            assert isinstance(result, Reply), f'return of power down must be instance Reply'
            ret_code = self.ReturnCode.SUCCESS if result.return_code == 0 else self.ReturnCode.ERROR
            return (ret_code, Reply.get_reply_result(result.return_code))
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','send power down timeout')))

    async def get_threshold_level(self, timeout_second) -> tuple:
        msg = GetThresholdLevel()
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            if isinstance(result, Reply):
                return (self.ReturnCode.INFO, Reply.get_reply_result(result.return_code))
            else:
                return (self.ReturnCode.SUCCESS, result)
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','get threshold level timeout')))

    async def set_threshold_level(self, threshold_level, timeout_second) -> tuple:
        msg = SetThresholdLevel(threshold_level)
        result = await self._commit_msg(msg, timeout_second)
        if result is not None:
            assert isinstance(result, Reply), f'return of set threshold level must be instance Reply'
            ret_code = self.ReturnCode.SUCCESS if result.return_code == 0 else self.ReturnCode.ERROR
            return (ret_code, Reply.get_reply_result(result.return_code))
        else:
            return (self.ReturnCode.ERROR, (0, 0, self._translate('sync_api','set threshold level timeout')))

    async def _commit_msg(self, msg: object, timeout_second: int):
        future = asyncio.Future()
        self._record_running_cmd((msg.msg_id, future))
        self.writer.commit_msg(msg)
        try:
            timeout_second = max(0, timeout_second)
            return await asyncio.wait_for(future, timeout_second)
        except asyncio.TimeoutError as e:
            return None

    async def _commit_special_msg(self, msg: SpecialCmd, timeout_second: int):
        future = asyncio.Future()
        self._record_running_cmd((msg.msg_id, future))
        try:
            timeout_second = max(0, timeout_second)
            return await asyncio.wait_for(future, timeout_second)
        except asyncio.TimeoutError as e:
            return None

    def _mark_finished_cmds(self, msgid: int, result: Msg) -> None:
        with self.cmds_lock:
            future = self.running_cmds.pop(msgid, None)
            if future is not None:
                try:
                    if not future.done():
                        future.set_result(result)
                except asyncio.InvalidStateError as e:
                    logger.debug('future already done or cancelled')
                    pass
            else:
                # Only log warning for unexpected missing futures, not for notification-only messages
                if msgid != self.SpecialCmd.CmdID.WAIT_READY:
                    logger.debug(f'No future found for msgid: {msgid}')
                else:
                    logger.debug(f'Received NID_READY notification without waiting future (this is normal)')

    def _record_running_cmd(self, msgid_future: tuple) -> None:
        with self.cmds_lock:
            if (msgid_future[0] == MsgID.MID_UPLOAD_ALG_IMAGE[0] or
                msgid_future[0] == MsgID.MID_UPLOAD_RAW_IMAGE[0]):
                self.running_cmds[MsgID.MID_IMAGE[0]] = msgid_future[1]
                return
            self.running_cmds[msgid_future[0]] = msgid_future[1]

    def msg_handler(self, response_msg: object, raw_data: bytes) -> None:
        if response_msg is None:
            print(f'response_msg is None')
            return
        parsed_result = response_msg.parse(raw_data)
        if parsed_result is None:
            print("msg_handler: response_msg.parse returned None")
            return
        msg_type, msg_id, msg_result = parsed_result
        logger.debug(f'msg_handler: msg_type={msg_type}, msg_id={msg_id}, msg_result={msg_result}')
        if isinstance(msg_result, Reply):
            print(f'Reply object details: msgid={msg_result.msgid}, return_code={msg_result.return_code}')
        if msg_type is not None:
            if msg_type == MsgID.MID_REPLY[0]:
                self._mark_finished_cmds(msg_id, msg_result)
            elif msg_type == MsgID.MID_IMAGE[0]:
                class ImageDataWrapper:
                    def __init__(self, payload):
                        self.uploaded_data = payload
                self._mark_finished_cmds(msg_type, ImageDataWrapper(msg_result))
            elif msg_type == MsgID.MID_NOTE[0]:
                notify_id = msg_id
                if notify_id == Notify.NotifyID.NID_LABEL_STATE:
                    label_state = msg_result
                    try:
                        if label_state.label_state_str.__contains__(f"{label_state.state}"):
                            face_state_tuple = label_state.label_state_str[f"{label_state.state}"]
                            # Assuming get_yaw, etc. are still relevant or will be added
                            self.notify_signal.face_state_signal.emit(face_state_tuple, 0, 0, 0)
                        else:
                            # Handle unknown state
                            pass
                    except Exception as e:
                        pass
                elif notify_id == Notify.NotifyID.NID_READY:
                    # Check if there's a future waiting for NID_READY
                    with self.cmds_lock:
                        if self.SpecialCmd.CmdID.WAIT_READY in self.running_cmds:
                            self._mark_finished_cmds(self.SpecialCmd.CmdID.WAIT_READY, Notify())
                        else:
                            logger.debug("Received NID_READY notification but no wait_ready command is pending")
                elif notify_id == Notify.NotifyID.NID_OTA_DONE:
                    result_state = msg_result
                    result = result_state.result
                    self.ota_signal.ota_signal.emit(result)
                else:
                    pass
            else:
                assert False, f'should not come here'
        else:
            print('None message')
