<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="zh_CN" sourcelanguage="en_US">
<context>
    <name>Dialog</name>
    <message>
        <location filename="../HI10Win.py" line="581"/>
        <source>connect module failed:
<byte value="xd"/>
                                                1.please confirm serial connection is OK</source>
        <translation>连接模块失败：
请确认串口连接正常</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="597"/>
        <source>open serial failed</source>
        <translation>打开串口失败</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="754"/>
        <source>Tool version :</source>
        <translation>工具版本：</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="762"/>
        <source>FW version :</source>
        <translation>固件版本：</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="775"/>
        <source>software version is </source>
        <translation>软件版本：</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="782"/>
        <source>FW version is </source>
        <translation>固件版本：</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="783"/>
        <source>HI10 APP </source>
        <translation>HI10 应用程序</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="919"/>
        <source>Set Threshold Level</source>
        <translation>设置阈值等级</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="919"/>
        <source>Enter threshold level (0-255):</source>
        <translation>输入阈值等级 (0-255):</translation>
    </message>
</context>
<context>
    <name>LogTextEdit</name>
    <message>
        <location filename="../HI10Win.py" line="305"/>
        <source>Log preview: {log_text[:preview_length]}...</source>
        <translation>日志预览: {log_text[:preview_length]}...</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="307"/>
        <source>Log file contains only whitespace</source>
        <translation>日志文件只包含空白字符</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="310"/>
        <source>Binary log preview: {hex_preview}...</source>
        <translation>二进制日志预览: {hex_preview}...</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="477"/>
        <source>module is busy, please wait for a while</source>
        <translation>模块忙碌中，请稍等</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="480"/>
        <source>calibration data error!</source>
        <translation>校准数据错误！</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="487"/>
        <source>module is not ready! 
<byte value="xd"/>
            check serial port configuration</source>
        <translation>模块未准备好！
请检查串口配置</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="498"/>
        <source>send power down success</source>
        <translation>发送关机命令成功</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="500"/>
        <source>send power down failed</source>
        <translation>发送关机命令失败</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="1128"/>
        <source>Not connected to device</source>
        <translation>未连接到设备</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="511"/>
        <source>Downloading log file...</source>
        <translation>正在下载日志文件...</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="518"/>
        <source>Log file saved: {log_filename} ({len(log_data)} bytes)</source>
        <translation>日志文件已保存: {log_filename} ({len(log_data)} 字节)</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="523"/>
        <source>No log data available</source>
        <translation>无可用日志数据</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="525"/>
        <source>Failed to download log file: {ret}</source>
        <translation>下载日志文件失败: {ret}</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="593"/>
        <source>connect successfully</source>
        <translation>连接成功</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="644"/>
        <source>disconnect</source>
        <translation>断开连接</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="722"/>
        <source>Label state notifications disabled</source>
        <translation>标签状态通知已禁用</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="725"/>
        <source>Sending reset command...</source>
        <translation>正在发送重置命令...</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="729"/>
        <source>Reset command sent successfully</source>
        <translation>重置命令发送成功</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="731"/>
        <source>Failed to send reset command</source>
        <translation>发送重置命令失败</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="733"/>
        <source>Error sending reset command: {str(e)}</source>
        <translation>发送重置命令错误: {str(e)}</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="836"/>
        <source>Getting firmware version...</source>
        <translation>正在获取固件版本...</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="841"/>
        <source>Firmware version: {fw_version}</source>
        <translation>固件版本: {fw_version}</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="843"/>
        <source>Failed to get firmware version</source>
        <translation>获取固件版本失败</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="845"/>
        <source>Getting algorithm version...</source>
        <translation>正在获取算法版本...</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="850"/>
        <source>Algorithm version: {lib_version}</source>
        <translation>算法版本: {lib_version}</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="852"/>
        <source>Failed to get algorithm version</source>
        <translation>获取算法版本失败</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="862"/>
        <source>Getting log size...</source>
        <translation>正在获取日志大小...</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="868"/>
        <source>Log file size: {log_size} bytes</source>
        <translation>日志文件大小: {log_size} 字节</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="870"/>
        <source>Failed to get log file size</source>
        <translation>获取日志文件大小失败</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="882"/>
        <source>Getting threshold level...</source>
        <translation>正在获取阈值等级...</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="888"/>
        <source>Current threshold level: {threshold_level}</source>
        <translation>当前阈值等级: {threshold_level}</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="890"/>
        <source>Failed to get threshold level</source>
        <translation>获取阈值等级失败</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="900"/>
        <source>Getting algorithm library version...</source>
        <translation>正在获取算法库版本...</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="905"/>
        <source>Algorithm library version: {lib_version}</source>
        <translation>算法库版本: {lib_version}</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="907"/>
        <source>Failed to get algorithm library version</source>
        <translation>获取算法库版本失败</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="927"/>
        <source>Setting threshold level to: {threshold_level}</source>
        <translation>设置阈值等级为: {threshold_level}</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="931"/>
        <source>Threshold level set successfully</source>
        <translation>阈值等级设置成功</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="933"/>
        <source>Failed to set threshold level</source>
        <translation>设置阈值等级失败</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="943"/>
        <source>Getting module status...</source>
        <translation>正在获取模块状态...</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="948"/>
        <source>Module status: {status_str}</source>
        <translation>模块状态: {status_str}</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="950"/>
        <source>Failed to get module status</source>
        <translation>获取模块状态失败</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="960"/>
        <source>Resetting module...</source>
        <translation>正在重置模块...</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="964"/>
        <source>Module reset successfully</source>
        <translation>模块重置成功</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="966"/>
        <source>Failed to reset module</source>
        <translation>模块重置失败</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="976"/>
        <source>Getting raw image size...</source>
        <translation>正在获取原始图像大小...</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="981"/>
        <source>Raw image size: {size} bytes</source>
        <translation>原始图像大小: {size} 字节</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="983"/>
        <source>Failed to get raw image size</source>
        <translation>获取原始图像大小失败</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="1221"/>
        <source>Module is not ready for image capture.</source>
        <translation>模块未准备好进行图像捕获。</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="1008"/>
        <source>Getting algorithm image size...</source>
        <translation>正在获取算法图像大小...</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="1013"/>
        <source>Algorithm image size: {size} bytes</source>
        <translation>算法图像大小: {size} 字节</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="1015"/>
        <source>Failed to get algorithm image size</source>
        <translation>获取算法图像大小失败</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="1042"/>
        <source>Please enter a command</source>
        <translation>请输入命令</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="1045"/>
        <source>Sending command: {cmd_text}</source>
        <translation>正在发送命令: {cmd_text}</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="1049"/>
        <source>Command sent: {cmd_text}</source>
        <translation>命令已发送: {cmd_text}</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="1131"/>
        <source>Auto capturing algorithm image...</source>
        <translation>自动捕获算法图像...</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="1277"/>
        <source>successfully set baudrate</source>
        <translation>成功设置波特率</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="1280"/>
        <source>failed to set baudrate</source>
        <translation>设置波特率失败</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="689"/>
        <source>recognize time</source>
        <translation>识别时间</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="701"/>
        <source>Recognition completed {counter} times: {recognize_success_cnt} successful</source>
        <translation>识别完成 {counter} 次: {recognize_success_cnt} 次成功</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="702"/>
        <source>, {recognize_unknown_failed_cnt} unknown users</source>
        <translation>, {recognize_unknown_failed_cnt} 次未知用户</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="703"/>
        <source>, {recognize_time_out_failed_cnt} timeouts</source>
        <translation>, {recognize_time_out_failed_cnt} 次超时</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="714"/>
        <source>Stopping recognition, disabling label state notifications and sending reset command...</source>
        <translation>停止识别，禁用标签状态通知并发送重置命令...</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="742"/>
        <source>Recognition stop timeout, forcing stop</source>
        <translation>识别停止超时，强制停止</translation>
    </message>
</context>
<context>
    <name>MainWindow</name>
    <message>
        <location filename="../HI10Win.py" line="665"/>
        <source>wrong parament format</source>
        <translation>参数格式错误</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="675"/>
        <source>left times: </source>
        <translation>剩余次数：</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="797"/>
        <source>MainWindow</source>
        <translation>HI10工具</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="798"/>
        <source>Serial Port</source>
        <translation>串口</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="799"/>
        <source>select com port to connect to device</source>
        <translation>选择连接设备的串口</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="800"/>
        <source>select baudrate for serial port</source>
        <translation>选择串口波特率</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="829"/>
        <source>connect to device</source>
        <translation>连接设备</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="802"/>
        <source>Connect</source>
        <translation>连接</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="831"/>
        <source>disconnect from device</source>
        <translation>断开设备连接</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="804"/>
        <source>Disconnect</source>
        <translation>断开</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="805"/>
        <source>Functions</source>
        <translation>功能</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="807"/>
        <source>start recognize</source>
        <translation>开始识别</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="808"/>
        <source>Start Recognize</source>
        <translation>开始识别</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="810"/>
        <source>Stop Recognize</source>
        <translation>停止识别</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="841"/>
        <source>snap image from camera</source>
        <translation>从摄像头抓取图像</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="812"/>
        <source>Snap</source>
        <translation>抓图</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="843"/>
        <source>read snaped image</source>
        <translation>读取已抓取的图像</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="817"/>
        <source>SendCmd</source>
        <translation>发送命令</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="818"/>
        <source>Image</source>
        <translation>图像</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="819"/>
        <source>Logs</source>
        <translation>日志</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="820"/>
        <source>show some log message</source>
        <translation>显示一些日志信息</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="821"/>
        <source>&amp;SerialPort</source>
        <translation>串口(&amp;S)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="822"/>
        <source>&amp;MainFunction</source>
        <translation>主要功能(&amp;M)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="823"/>
        <source>&amp;Debug</source>
        <translation>调试(&amp;D)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="824"/>
        <source>&amp;Help</source>
        <translation>帮助(&amp;H)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="825"/>
        <source>&amp;Configuration</source>
        <translation>配置(&amp;C)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="827"/>
        <source>&amp;OTA</source>
        <translation>固件升级(&amp;O)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="828"/>
        <source>&amp;Connect</source>
        <translation>连接(&amp;C)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="830"/>
        <source>Disconnec&amp;t</source>
        <translation>断开连接(&amp;t)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="832"/>
        <source>&amp;Register</source>
        <translation>注册(&amp;R)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="833"/>
        <source>register new user from giving image</source>
        <translation>从给定图像注册新用户</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="836"/>
        <source>&amp;DeleteAllUser</source>
        <translation>删除所有用户(&amp;D)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="837"/>
        <source>delete all registerd face from device</source>
        <translation>从设备中删除所有已注册的人脸</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="838"/>
        <source>GetUserFeature</source>
        <translation>获取用户特征</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="839"/>
        <source>get user feature</source>
        <translation>获取用户特征</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="840"/>
        <source>&amp;Snap</source>
        <translation>抓图(&amp;S)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="842"/>
        <source>&amp;ReadImage</source>
        <translation>读取图像(&amp;R)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="844"/>
        <source>&amp;About</source>
        <translation>关于(&amp;A)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="845"/>
        <source>about facelock app</source>
        <translation>关于人脸锁应用</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="846"/>
        <source>&amp;TimeStatistic</source>
        <translation>时间统计(&amp;T)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="848"/>
        <source>&amp;Open OTA Window</source>
        <translation>打开OTA窗口(&amp;O)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="849"/>
        <source>open OTA window and start OTA</source>
        <translation>打开OTA窗口并开始OTA升级</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="850"/>
        <source>&amp;Send File</source>
        <translation>发送文件(&amp;S)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="851"/>
        <source>RegisterFrom&amp;Image</source>
        <translation>从图像注册(&amp;I)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="857"/>
        <source>Lowest</source>
        <translation>最低</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="858"/>
        <source>Low</source>
        <translation>低</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="859"/>
        <source>Normal</source>
        <translation>正常</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="860"/>
        <source>High</source>
        <translation>高</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="861"/>
        <source>Highest</source>
        <translation>最高</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="862"/>
        <source>ActionStub</source>
        <translation>动作桩</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="866"/>
        <source>DeleteKey</source>
        <translation>删除密钥</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="864"/>
        <source>Enter</source>
        <translation>进入</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="865"/>
        <source>Exit</source>
        <translation>退出</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="867"/>
        <source>delete encryption key</source>
        <translation>删除密钥</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="868"/>
        <source>RegisterWithFeature</source>
        <translation>用特征注册</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="869"/>
        <source>register face with feature file</source>
        <translation>使用特征文件注册人脸</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="870"/>
        <source>RegisterSingle</source>
        <translation>单次注册</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="871"/>
        <source>register with single position face</source>
        <translation>使用单角度人脸注册</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="872"/>
        <source>GetAllUserInfo</source>
        <translation>获取所有用户信息</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="873"/>
        <source>get all user info</source>
        <translation>获取所有用户信息</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="874"/>
        <source>Open SendFile Window</source>
        <translation>打开发送文件窗口</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="875"/>
        <source>enter_debug_mode</source>
        <translation>进入调试模式</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="876"/>
        <source>exit_debug_mode</source>
        <translation>退出调试模式</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="877"/>
        <source>RegisterBatchFeatures</source>
        <translation>批量特征注册</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="878"/>
        <source>RegisterBatchImages</source>
        <translation>批量图像注册</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="879"/>
        <source>CaptureWhenSuccess</source>
        <translation>成功时捕获</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="880"/>
        <source>CaptureWhenFailed</source>
        <translation>失败时捕获</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="881"/>
        <source>Auto&amp;Capture</source>
        <translation>自动抓图(&amp;C)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="882"/>
        <source>ChangeCaptureMode</source>
        <translation>更改捕获模式</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="883"/>
        <source>RegisterIntegrated</source>
        <translation>集成注册</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="884"/>
        <source>GetLogFiles</source>
        <translation>获取日志文件</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="885"/>
        <source>RegisterBatchedRGBImages</source>
        <translation>批量RGB图像注册</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="886"/>
        <source>RegisterRGBImage</source>
        <translation>RGB图像注册</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="887"/>
        <source>RegisterBatchedIRImages</source>
        <translation>批量红外图像注册</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="888"/>
        <source>RegisterIRImage</source>
        <translation>红外图像注册</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="889"/>
        <source>PowerDown</source>
        <translation>关机</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="890"/>
        <source>版本修改点</source>
        <translation>版本修改点</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="891"/>
        <source>双目原图抓拍</source>
        <translation>双目原图抓拍</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="892"/>
        <source>RegisterAlignedIRImage</source>
        <translation>对齐红外图像注册</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="893"/>
        <source>RegisterBatchAlignedIRImage</source>
        <translation>批量对齐红外图像注册</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="894"/>
        <source>JumpFw1WithAcm</source>
        <translation>通过ACM跳转至固件1</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="895"/>
        <source>Get Version</source>
        <translation>获取版本</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="896"/>
        <source>get device version information</source>
        <translation>获取设备版本信息</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="897"/>
        <source>Get Log Size</source>
        <translation>获取日志大小</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="898"/>
        <source>get log size from device</source>
        <translation>从设备获取日志大小</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="899"/>
        <source>Get Threshold</source>
        <translation>获取阈值</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="900"/>
        <source>get threshold settings from device</source>
        <translation>从设备获取阈值设置</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="901"/>
        <source>Label Recognition</source>
        <translation>标签识别</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="902"/>
        <source>start label recognition</source>
        <translation>开始标签识别</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="903"/>
        <source>Get Lib Model Version</source>
        <translation>获取库模型版本</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="904"/>
        <source>get algorithm library and model library version information</source>
        <translation>获取算法库和模型库版本信息</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="905"/>
        <source>Set Threshold</source>
        <translation>设置阈值</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="906"/>
        <source>set recognition threshold level</source>
        <translation>设置识别阈值等级</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="907"/>
        <source>Get Status</source>
        <translation>获取状态</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="908"/>
        <source>get current module status immediately</source>
        <translation>立即获取当前模块状态</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="909"/>
        <source>Reset Module</source>
        <translation>重置模块</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="910"/>
        <source>stop current processing, module enters standby</source>
        <translation>停止当前处理，模块进入待机状态</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="911"/>
        <source>Get Raw Image Size</source>
        <translation>获取原始图像大小</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="912"/>
        <source>get the size of raw image to be uploaded</source>
        <translation>获取待上传的原始图像大小</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="913"/>
        <source>Upload Raw Image</source>
        <translation>上传原始图像</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="914"/>
        <source>upload raw image to host</source>
        <translation>将原始图像上传到主机</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="915"/>
        <source>Get Algorithm Image Size</source>
        <translation>获取算法图像大小</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="916"/>
        <source>get the size of algorithm image to be uploaded</source>
        <translation>获取待上传的算法图像大小</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="917"/>
        <source>Upload Algorithm Image</source>
        <translation>上传算法图像</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="918"/>
        <source>upload algorithm image to host</source>
        <translation>将算法图像上传到主机</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="806"/>
        <source>cont_recognize</source>
        <translation>连续识别</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="809"/>
        <source>stop recognize</source>
        <translation>停止识别</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="826"/>
        <source>RecognizeSafetyLevel</source>
        <translation>识别安全级别</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="834"/>
        <source>&amp;Recognize</source>
        <translation>识别(&amp;R)</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="835"/>
        <source>recognize current face in front of camera</source>
        <translation>识别摄像头前的当前人脸</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="847"/>
        <source>time statistic for recognize process</source>
        <translation>识别过程的时间统计</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="813"/>
        <source>read raw image</source>
        <translation>读取raw图像</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="814"/>
        <source>Read RAW</source>
        <translation>读取raw图</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="815"/>
        <source>read algorithm image</source>
        <translation>读取算法图</translation>
    </message>
    <message>
        <location filename="../ui/MainWindow.py" line="816"/>
        <source>Read ALG</source>
        <translation>读算法图</translation>
    </message>
</context>
<context>
    <name>OTAWindow</name>
    <message>
        <location filename="../src/OtaWindow.py" line="117"/>
        <source>choose firmware file:
</source>
        <translation>选择固件文件：
</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="128"/>
        <source>successfully enter upgrade mode</source>
        <translation>成功进入升级模式</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="130"/>
        <source>get the status = </source>
        <translation>获取状态 = </translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="131"/>
        <source>next pid = </source>
        <translation>下一个包序号 = </translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="135"/>
        <source>successfully send OTA header</source>
        <translation>成功发送OTA头部</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="137"/>
        <source>successfully send OTA data, pid = </source>
        <translation>成功发送OTA数据，包序号 = </translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="140"/>
        <source>successfully stop OTA</source>
        <translation>成功停止OTA</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="205"/>
        <source>please choose txt file named firmware_update.txt</source>
        <translation>请选择名为firmware_update.txt的txt文件</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="267"/>
        <source>error ota file</source>
        <translation>OTA文件错误</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="280"/>
        <source>OTA update successfully, rebooting device...</source>
        <translation>OTA升级成功，正在重启设备...</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="291"/>
        <source>Reboot command sent, device is restarting...</source>
        <translation>重启命令已发送，设备正在重启...</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="293"/>
        <source>OTA completed, please manually restart the device</source>
        <translation>OTA完成，请手动重启设备</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="296"/>
        <source>OTA completed, please manually restart the device</source>
        <translation>OTA完成，请手动重启设备</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="282"/>
        <source>OTA update failed, please retry!</source>
        <translation>OTA升级失败，请重试！</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="310"/>
        <source>ota file name is not valid</source>
        <translation>OTA文件名无效</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="331"/>
        <source>unrecognized file type</source>
        <translation>无法识别的文件类型</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="336"/>
        <source>format is not valid!</source>
        <translation>格式无效！</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="346"/>
        <source>successfully set baudrate</source>
        <translation>成功设置波特率</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="350"/>
        <source>failed to set baudrate</source>
        <translation>设置波特率失败</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="393"/>
        <source>start transfering file, please wait......</source>
        <translation>开始传输文件，请等待......</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="416"/>
        <source>file data transfer over.
</source>
        <translation>文件数据传输完成。
</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="417"/>
        <source>waiting for firmware burning flash......</source>
        <translation>等待固件烧录到闪存......</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="432"/>
        <source>successfully upgrade firmware!</source>
        <translation>成功升级固件！</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="437"/>
        <source>failed to upgrade firmware!</source>
        <translation>升级固件失败！</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="463"/>
        <source>read json cfg data error!</source>
        <translation>读取json配置数据错误！</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="489"/>
        <source>send json cfg data error!
</source>
        <translation>发送json配置数据错误！
</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="494"/>
        <source>successfully send json cfg data, pid = </source>
        <translation>成功发送json配置数据，包序号 = </translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="502"/>
        <source>send end packet error!
</source>
        <translation>发送结束包错误！
</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="589"/>
        <source>successfully send end packet!
</source>
        <translation>成功发送结束包！
</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="595"/>
        <source>successfully write file to fs .
</source>
        <translation>成功将文件写入文件系统。
</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="598"/>
        <source>failed write file to fs.
</source>
        <translation>写入文件到文件系统失败。
</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="538"/>
        <source>successfully compare upload and download json cfg
</source>
        <translation>成功比对上传和下载的json配置
</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="543"/>
        <source>failed compare upload and download json cfg
</source>
        <translation>比对上传和下载的json配置失败
</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="547"/>
        <source>upload facelock_cfg.json exception!
</source>
        <translation>上传facelock_cfg.json异常！
</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="586"/>
        <source>send file error!
</source>
        <translation>发送文件错误！
</translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="577"/>
        <source>successfully send data, pid = </source>
        <translation>成功发送数据，包序号 = </translation>
    </message>
    <message>
        <location filename="../src/OtaWindow.py" line="555"/>
        <source>file name match error!</source>
        <translation>文件名匹配错误！</translation>
    </message>
</context>
<context>
    <name>OTAWindowUI</name>
    <message>
        <location filename="../ui/OtaWindowUI.py" line="89"/>
        <source>OTA test tool</source>
        <translation>OTA测试工具</translation>
    </message>
    <message>
        <location filename="../ui/OtaWindowUI.py" line="90"/>
        <source>selectfile</source>
        <translation>选择文件</translation>
    </message>
    <message>
        <location filename="../ui/OtaWindowUI.py" line="91"/>
        <source>start</source>
        <translation>开始</translation>
    </message>
    <message>
        <location filename="../ui/OtaWindowUI.py" line="92"/>
        <source>.txt</source>
        <translation>.txt</translation>
    </message>
    <message>
        <location filename="../ui/OtaWindowUI.py" line="93"/>
        <source>.ota</source>
        <translation>.ota</translation>
    </message>
    <message>
        <location filename="../ui/OtaWindowUI.py" line="94"/>
        <source>.json</source>
        <translation>.json</translation>
    </message>
    <message>
        <location filename="../ui/OtaWindowUI.py" line="95"/>
        <source>bps:</source>
        <translation>波特率：</translation>
    </message>
    <message>
        <location filename="../ui/OtaWindowUI.py" line="96"/>
        <source>pkt:</source>
        <translation>包：</translation>
    </message>
</context>
<context>
    <name>Protocol</name>
    <message>
        <location filename="../src/hi10_protocol.py" line="22"/>
        <source>模块对主控命令的应答</source>
        <translation>模块对主控命令的应答</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="23"/>
        <source>模块主动发给主控的信息</source>
        <translation>模块主动发给主控的信息</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="24"/>
        <source>模块给主控传送图片</source>
        <translation>模块给主控传送图片</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="26"/>
        <source>停止当前处理，模块进入 standby</source>
        <translation>停止当前处理，模块进入待机状态</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="27"/>
        <source>立即返回模块当前状态</source>
        <translation>立即返回模块当前状态</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="28"/>
        <source>标签识别</source>
        <translation>标签识别</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="29"/>
        <source>设置识别阈值等级</source>
        <translation>设置识别阈值等级</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="30"/>
        <source>获取识别阈值等级</source>
        <translation>获取识别阈值等级</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="31"/>
        <source>获得固件版本信息</source>
        <translation>获得固件版本信息</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="32"/>
        <source>获得算法库与模型库版本信息</source>
        <translation>获得算法库与模型库版本信息</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="33"/>
        <source>获取日志文件大小</source>
        <translation>获取日志文件大小</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="34"/>
        <source>上传日志文件到主控</source>
        <translation>上传日志文件到主控</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="35"/>
        <source>获取待上传 raw 图片大小</source>
        <translation>获取待上传的原始图片大小</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="36"/>
        <source>上传 raw 图片到主控</source>
        <translation>上传原始图片到主控</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="37"/>
        <source>获取待上传算法小图大小</source>
        <translation>获取待上传算法小图大小</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="38"/>
        <source>上传算法小图到主控</source>
        <translation>上传算法小图到主控</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="39"/>
        <source>OTA 模式下设定通信口波特率</source>
        <translation>OTA模式下设定通信口波特率</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="40"/>
        <source>进入 OTA 升级模式</source>
        <translation>进入OTA升级模式</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="41"/>
        <source>退出 OTA 模式，模组重启</source>
        <translation>退出OTA模式，模组重启</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="42"/>
        <source>获取 OTA 状态及传输起始包序号</source>
        <translation>获取OTA状态及传输起始包序号</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="43"/>
        <source>发送升级包元数据</source>
        <translation>发送升级包元数据</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="44"/>
        <source>发送升级包数据</source>
        <translation>发送升级包数据</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="45"/>
        <source>模块下电</source>
        <translation>模块下电</translation>
    </message>
</context>
<context>
    <name>ProtocolRetStr</name>
    <message>
        <location filename="../src/hi10_protocol.py" line="579"/>
        <source>success</source>
        <translation>成功</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="580"/>
        <source>module rejected this command</source>
        <translation>模块拒绝此命令</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="581"/>
        <source>algo aborted</source>
        <translation>算法中止</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="587"/>
        <source>padding code, protocol not define this code</source>
        <translation>填充代码，协议未定义此代码</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="583"/>
        <source>camera open failed</source>
        <translation>摄像头打开失败</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="584"/>
        <source>unknown error</source>
        <translation>未知错误</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="585"/>
        <source>invalid parameter</source>
        <translation>无效参数</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="586"/>
        <source>no enough memory</source>
        <translation>内存不足</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="588"/>
        <source>exceed the time limit</source>
        <translation>超时</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="589"/>
        <source>read file failed</source>
        <translation>读取文件失败</translation>
    </message>
    <message>
        <location filename="../src/hi10_protocol.py" line="590"/>
        <source>write file failed</source>
        <translation>写入文件失败</translation>
    </message>
</context>
<context>
    <name>SendFileLog</name>
    <message>
        <location filename="../src/SendFileWindow.py" line="42"/>
        <source>choose file:
</source>
        <translation>选择文件：
</translation>
    </message>
    <message>
        <location filename="../src/SendFileWindow.py" line="70"/>
        <source>success</source>
        <translation>成功</translation>
    </message>
    <message>
        <location filename="../src/SendFileWindow.py" line="98"/>
        <source>please choose a file!
</source>
        <translation>请选择一个文件！
</translation>
    </message>
    <message>
        <location filename="../src/SendFileWindow.py" line="103"/>
        <source>file is too big!
</source>
        <translation>文件太大！
</translation>
    </message>
    <message>
        <location filename="../src/SendFileWindow.py" line="123"/>
        <source>send file error!</source>
        <translation>发送文件错误！</translation>
    </message>
    <message>
        <location filename="../src/SendFileWindow.py" line="128"/>
        <source>successfully send data, pid =</source>
        <translation>成功发送数据，pid =</translation>
    </message>
    <message>
        <location filename="../src/SendFileWindow.py" line="136"/>
        <source>send file error!
</source>
        <translation>发送文件错误！
</translation>
    </message>
    <message>
        <location filename="../src/SendFileWindow.py" line="139"/>
        <source>successfully send end packet!
</source>
        <translation>成功发送结束包！
</translation>
    </message>
</context>
<context>
    <name>SendFileWindow</name>
    <message>
        <location filename="../ui/SendFileWindowUI.py" line="67"/>
        <source>Send File</source>
        <translation>发送文件</translation>
    </message>
    <message>
        <location filename="../ui/SendFileWindowUI.py" line="68"/>
        <source>select file</source>
        <translation>选择文件</translation>
    </message>
    <message>
        <location filename="../ui/SendFileWindowUI.py" line="69"/>
        <source>start</source>
        <translation>开始</translation>
    </message>
    <message>
        <location filename="../ui/SendFileWindowUI.py" line="70"/>
        <source>write to fs</source>
        <translation>写入文件系统</translation>
    </message>
</context>
<context>
    <name>Speaker</name>
    <message>
        <location filename="../HI10Win.py" line="693"/>
        <source>Recognition successful</source>
        <translation>识别成功</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="698"/>
        <source>Recognition failed</source>
        <translation>识别失败</translation>
    </message>
    <message>
        <location filename="../HI10Win.py" line="1248"/>
        <source>Please face the camera directly</source>
        <translation>请正对摄像头</translation>
    </message>
</context>
<context>
    <name>sync_api</name>
    <message>
        <location filename="../src/sync_api.py" line="74"/>
        <source>reset timeout</source>
        <translation>复位超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="85"/>
        <source>wait_ready timeout</source>
        <translation>等待就绪超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="98"/>
        <source>get_module_status timeout</source>
        <translation>获取模块状态超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="120"/>
        <source>read_img failed</source>
        <translation>读取图像失败</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="122"/>
        <source>get_snaped_imgsize failed</source>
        <translation>获取抓拍图像尺寸失败</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="219"/>
        <source>get_logfile failed</source>
        <translation>获取日志文件失败</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="230"/>
        <source>temperature_level</source>
        <translation>温度等级</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="230"/>
        <source>with cloth background</source>
        <translation>布料背景</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="232"/>
        <source>verify timeout</source>
        <translation>验证超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="244"/>
        <source>config baudrate timeout</source>
        <translation>配置波特率超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="255"/>
        <source>start_ota timeout</source>
        <translation>启动OTA超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="267"/>
        <source>get_ota_status timeout</source>
        <translation>获取OTA状态超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="278"/>
        <source>send_ota_header timeout</source>
        <translation>发送OTA头部超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="289"/>
        <source>send_ota_packet timeout</source>
        <translation>发送OTA数据包超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="300"/>
        <source>stop_ota timeout</source>
        <translation>停止OTA超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="311"/>
        <source>send file timeout</source>
        <translation>发送文件超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="322"/>
        <source>get firmware version timeout</source>
        <translation>获取固件版本超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="333"/>
        <source>get lib version timeout</source>
        <translation>获取库版本超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="344"/>
        <source>get board sn timeout</source>
        <translation>获取板卡序列号超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="354"/>
        <source>send file info timeout</source>
        <translation>发送文件信息超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="368"/>
        <source>cmd parse timeout</source>
        <translation>命令解析超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="378"/>
        <source>send power down timeout</source>
        <translation>发送关机命令超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="389"/>
        <source>get threshold level timeout</source>
        <translation>获取阈值等级超时</translation>
    </message>
    <message>
        <location filename="../src/sync_api.py" line="399"/>
        <source>set threshold level timeout</source>
        <translation>设置阈值等级超时</translation>
    </message>
</context>
</TS>
