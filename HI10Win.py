#! /usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import asyncio
import functools
from typing import Iterator, <PERSON><PERSON>
from PyQt5.QtCore import QSettings, pyqtSlot, QTranslator
from PyQt5 import QtWidgets
from PyQt5.QtWidgets import QMainWindow, QWidget, QLabel, QMessageBox, QApplication, QActionGroup, QWidgetAction, QLineEdit
from PyQt5.QtCore import Qt, QSignalBlocker
from src.hi10_protocol import *
from PyQt5.QtGui import QCloseEvent,QImage,QPixmap, QIcon, QColor, QColorConstants, QFont
from src.OtaWindow import OTAWindow
from pathlib import Path
from datetime import datetime
from src.serialwraper import SerialWraper
from src.sync_api import HI10
from qasync import QEventLoop, asyncSlot, asyncClose
from src.SendFileWindow import Send<PERSON>ileWindow
# from keyboard_monitor import KeyMonitor
from src.json_transform import *
from src.image_capture import *
from src.config import *
# TODO use platform unrelated functions, this is important
import serial
from serial.tools.list_ports import comports
from ui.MainWindow import Ui_MainWindow
import numpy as np
import logging
logging.basicConfig(level=logging.DEBUG)


def resource_path(relative_path):
    """ Get absolute path to resource, works for dev and for PyInstaller """
    base_path = getattr(sys, '_MEIPASS', os.path.dirname(os.path.abspath(__file__)))
    return os.path.join(base_path, relative_path)

def to_valid_filename(strs: str) -> str:
    return ''.join([x if x.isalnum() else "_" for x in strs])

if GlobalConfigParam.CFG_ENABLE_SPEAKER:
    from src.speaker import Speaker
else:
    from src.speaker import SpeakerStub as Speaker

# Setting constants
SETTING_PORT_NAME = 'port_name'
SETTING_BAUD_RATE = 'baudrate'
SETTING_LANGUAGE  = 'language'

def get_serial_ports() -> Iterator[Tuple[str, str]]:
    """Return all available serial ports."""
    ports = comports()
    return ((p.description, p.device) for p in ports)


def handleUIState(fn):
    """disable ui when enter, and enable ui when exit"""

    @functools.wraps(fn)
    def wrapper(self, *args, **kwargs):
        self.disable_ui(self.all_func_ui)
        f = asyncio.ensure_future(fn(self, *args, **kwargs))
        while not f.done():
            QApplication.instance().processEvents()
        
        # 根据连接状态启用/禁用UI
        self._update_ui_state()

        return f

    return wrapper

def timer(func):
    @functools.wraps(func)
    def wrapper_timer(*args, **kwargs):
        tic = time.perf_counter()
        value = func(*args, **kwargs)
        toc = time.perf_counter()
        elapsed_time = toc - tic
        print(f"Elapsed time: {elapsed_time:0.4f} seconds")
        return value
    return wrapper_timer

# noinspection PyArgumentList
class MainWindow(QMainWindow, Ui_MainWindow):

    """Main Window"""
    def __init__(self, *args, **kwargs) -> None:
        super(MainWindow, self).__init__(*args, **kwargs)
        self.setupUi(self)
        self.disconnect_btn.setVisible(False)
        self.action_Send_File.setVisible(False)
        self.snap_btn.setVisible(False)
        self.action_snap.setVisible(False)

        rect = QApplication.desktop().screenGeometry()

        x = int((rect.width()-640) / 2)
        y = int((rect.height()-400) / 2)
        self.setGeometry(x, y, 700, 450)

        self.__tool_version, self.__tool_name, self.__app_name= self.__get_tool_version()

        self.setWindowTitle(self.__tool_name)
        icon_path = resource_path("resource/logo.ico")
        self.setWindowIcon(QIcon(icon_path))

        load_cfg()

        self.load_language_settings()
        if self.language_using == 'chinese':
            self.on_action_language_chinese_triggered()
        if hasattr(self, "cmd_text"):
            # del self.cmd_text
            self.cmd_text.deleteLater()
            self.cmd_text = QLineEdit(self.widget_4)
            self.cmd_text.setEnabled(False)
            sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
            sizePolicy.setHorizontalStretch(0)
            sizePolicy.setVerticalStretch(0)
            sizePolicy.setHeightForWidth(self.cmd_text.sizePolicy().hasHeightForWidth())
            self.cmd_text.setSizePolicy(sizePolicy)
            self.cmd_text.setMinimumSize(QtCore.QSize(770, 30))
            self.cmd_text.setObjectName("cmd_text")
            self.horizontalLayout_6.addWidget(self.cmd_text)
            self.cmd_text.returnPressed.connect(self.__cmd_text_return_or_enter_signal_recv)
        self.port_names = []
        self.is_serial_port_init_ok = False
        self.serial_port_name_current_choose = ''
        self.serial_port_name_last_success = ''
        self.update_com_ports()
        self.port_combobox.currentIndexChanged.connect(self.combox_index_changed)
        #
        self.port_combobox.currentTextChanged.connect(self._save_settings)
        self.baudrate_combobox.currentTextChanged.connect(self._save_settings)
        self.cont_recognize_checkBox.stateChanged.connect(self.on_continue_recognize_checkbox_statechanged)
        #
        self.serial_port = serial.Serial()
        # start daemon timer
        self.serial_daemon_timer_id = self.startTimer(1500)
        # module status monitor task
        self.module_status_monitor_task = None
        self.module_status_monitor_task_running = False

        #
        self.speaker = Speaker(same_msg_cd_time_second=GlobalConfigParam.CFG_SAME_MSG_CD_TIME_SECOND)
        self.speaker.start()
        self.serial_wrapper = None
        self.hi10 = None
        #
        self.recognize_need_stop = False
        #
        self.__need_snap_raw_img = False
        #
        # 简化UI控件列表，减少重复代码
        self._init_ui_control_lists()
        self.first_level_menu_ui = [
            self.menuMainFunction,
            self.menuDebug,
            self.menuConfig,
            self.menuOTA,
            self.menuHelp,
        ]
        #
        self.status_label = QLabel()
        self.status_label.setMinimumSize(QtCore.QSize(940, 30))
        font = QFont()
        font.setPixelSize(13)
        self.status_label.setFont(font)
        self.statusBar.addPermanentWidget(self.status_label, 1)
        # simple user database
        self.user_db = []
        #
        #
        self.left_times_info_label = QLabel(self.container)
        # tmp_info_label.setGeometry(self.recognize_times_lineedit.geometry())
        self.left_times_info_label.setMinimumSize(QtCore.QSize(120, 25))
        self.left_times_info_label.setMaximumSize(QtCore.QSize(120, 25))
        try:
            self.horizontalLayout_2.insertWidget(0, self.left_times_info_label)
        except Exception as e:
            pass
        self.left_times_info_label.setVisible(False)
        #
        self._translate = QtCore.QCoreApplication.translate
        #
        self.is_ota_window_created = False
        #
        # if not GlobalConfigParam.CFG_ENABLE_DEBUG_MODE:
        if not GlobalConfigParam.CFG_ENABLE_SEND_FILE:
            self.action_Open_SendFile_Window.setVisible(False)
        self.__is_support_snap_small_raw_image = False
        try:
            if not GlobalConfigParam.CFG_ENABLE_SEND_CMD:
                self.cmd_text.setVisible(False)
                self.send_cmd.setVisible(False)
        except Exception as e:
            pass
        
        # 初始化时禁用所有功能UI，只有连接串口后才能使用
        self.disable_ui(self.all_func_ui)

    def _init_ui_control_lists(self):
        """初始化UI控件列表，减少重复代码"""
        # 基础控件
        basic_controls = [
            self.disconnect_btn, self.recognize_btn, self.stop_recognize_btn,
            self.read_raw_btn, self.read_alg_btn, self.snap_btn, self.cont_recognize_checkBox,
            self.recognize_times_lineedit, self.cmd_text, self.send_cmd,
        ]
        
        # 菜单控件
        menu_controls = [
            self.menuMainFunction, self.menuDebug,
            self.menuConfig, self.menuOTA, self.menuHelp
        ]
        
        # 基础动作
        basic_actions = [
            self.action_disconnect, self.action_recognize, self.action_readimage,
            self.action_autocapture, self.action_time_statistic, self.action_Open_OTA_Window,
            self.action_Open_SendFile_Window, self.action_Send_File, self.action_get_logfiles,
            self.action_power_down
        ]
        
        # 功能动作
        function_actions = [
            self.action_label_recog, self.action_get_version, self.action_get_lib_model_version,
            self.action_get_threshold, self.action_set_threshold, self.action_get_status,
            self.action_get_log_size, self.action_reset, self.action_get_raw_image_size,
            self.action_upload_raw_image, self.action_get_alg_image_size, self.action_upload_alg_image
        ]
        
        self.all_func_ui = basic_controls + menu_controls + basic_actions + function_actions

    def _update_ui_state(self):
        """根据连接状态更新UI状态"""
        if self.hi10 is not None and self.serial_port.is_open:
            self.enable_ui(self.all_func_ui)
        else:
            self.disable_ui(self.all_func_ui)

    def _save_file_with_timestamp(self, data, prefix, extension, file_type="other"):
        """保存文件并添加时间戳，根据文件类型创建相应文件夹"""
        import os
        import numpy as np

        # 根据文件类型确定文件夹名称
        folder_map = {
            "log": "logs",
            "raw_image": "raw_images",
            "alg_image": "alg_images",
            "other": "files"
        }

        folder_name = folder_map.get(file_type, "files")

        # 创建文件夹（如果不存在）
        if not os.path.exists(folder_name):
            os.makedirs(folder_name)
            print(f"Created directory: {folder_name}")

        timestamp = datetime.now().strftime('%Y_%m_%d_%H_%M_%S')

        # 注意：图像的PNG转换现在在显示时处理，这里只保存原始数据作为备份

        # 默认保存逻辑
        filename = f"{prefix}_{timestamp}.{extension}"
        filepath = os.path.join(folder_name, filename)

        with open(filepath, "wb") as f:
            f.write(data)

        return filepath

    def _get_adaptive_display_size(self):
        """获取自适应的显示尺寸，基于图像显示标签的当前大小"""
        # 获取图像显示标签的当前大小
        label_size = self.image_display_label.size()

        # 如果标签还没有正确的大小，使用默认值
        if label_size.width() < 100 or label_size.height() < 100:
            return QtCore.QSize(800, 400)

        # 留一些边距
        margin = 20
        return QtCore.QSize(
            max(label_size.width() - margin, 200),
            max(label_size.height() - margin, 150)
        )



    def _show_log_preview(self, log_data):
        """显示日志数据预览"""
        try:
            log_text = log_data.decode('utf-8', errors='replace')
            if log_text.strip():
                preview_length = min(200, len(log_text))
                self.show_result(self._translate('LogTextEdit', f'Log preview: {log_text[:preview_length]}...'), HI10.ReturnCode.INFO)
            else:
                self.show_result(self._translate('LogTextEdit', 'Log file contains only whitespace'), HI10.ReturnCode.WARNING)
        except Exception as e:
            hex_preview = ' '.join([f'{b:02X}' for b in log_data[:32]])
            self.show_result(self._translate('LogTextEdit', f'Binary log preview: {hex_preview}...'), HI10.ReturnCode.INFO)

    def _handle_exception(self, operation_name, exception):
        """统一的异常处理方法"""
        error_msg = f'{operation_name}: {str(exception)}'
        self.show_result(self._translate('LogTextEdit', error_msg), HI10.ReturnCode.ERROR)
        import traceback
        traceback.print_exc()

    def __cmd_text_return_or_enter_signal_recv(self):
        f = asyncio.ensure_future(self.on_send_cmd_pressed())
        while not f.done():
            QApplication.instance().processEvents()

    def load_language_settings(self) -> None:
        settings = QSettings('AIVA', 'HI10App')
        self.language_using = settings.value(SETTING_LANGUAGE)
        print(self.language_using)
        pass

    def _load_settings(self) -> None:
        """Load settings on startup."""
        settings = QSettings('AIVA', 'HI10App')
        
        # port name
        port_name = settings.value(SETTING_PORT_NAME)
        if port_name is not None:
            index = self.port_combobox.findData(port_name)
            if index > -1:
                self.port_combobox.setCurrentIndex(index)
        baudrate = settings.value(SETTING_BAUD_RATE)
        if baudrate is not None:
            index = self.baudrate_combobox.findData(baudrate)
            if index > -1:
                self.baudrate_combobox.setCurrentIndex(index)
    def _save_settings(self) -> None:
        """Save settings on shutdown."""
        settings = QSettings('AIVA', 'HI10App')
        # print('save setting')
        settings.setValue(SETTING_PORT_NAME, self.port)
        settings.setValue(SETTING_BAUD_RATE, self.baudrate)
        settings.sync()

    def show_error_message(self, msg: str) -> None:
        """Show a Message Box with the error message."""
        QMessageBox.critical(self, QApplication.applicationName(), str(msg))

    def update_com_ports(self) -> None:
        """Update COM Port list in GUI."""
        self.port_combobox.clear()
        new_port_names = []
        for name, device in get_serial_ports():
            new_port_names.append(device)
            self.port_combobox.addItem(device, device)
        if self.is_serial_port_init_ok:
            tmp = -1
            # new port is plugged
            if len(self.port_names) < len(new_port_names):
                tmp = self.port_combobox.findData(self.serial_port_name_last_success)
                if tmp != -1:
                    self.port_combobox.setCurrentIndex(tmp)
                else: 
                    i = 0
                    for index in range(len(self.port_names)):
                        if self.port_names[index] != new_port_names[index]:
                            break
                        else:
                            i += 1
                    tmp = self.port_combobox.findData(new_port_names[i])
                    self.port_combobox.setCurrentIndex(tmp)
                    # self.serial_port_name_current_choose = new_port_names[i]
                self._save_settings()
            # port is unplugged
            elif len(self.port_names) > len(new_port_names):
                self.port_combobox.setCurrentIndex(-1)
            else:
                if self.serial_port_name_current_choose != '':
                    tmp = self.port_combobox.findData(self.serial_port_name_current_choose)
                else:
                    if self.serial_port_name_last_success != '':
                        tmp = self.port_combobox.findData(self.serial_port_name_last_success)
                    else:
                        for index in range(0, len(new_port_names)):
                            # just for linux system, windows doesnot have a special name for serial port
                            # so we donot consider windows system here
                            if new_port_names[index].find("/dev/ttyUSB") != -1:
                                tmp = self.port_combobox.findData(new_port_names[index])
                                break
                            else:
                                tmp = -1
                self.port_combobox.setCurrentIndex(tmp)
                self._save_settings()
        else:
            self.is_serial_port_init_ok = True
        self.port_names = new_port_names

        if self.baudrate_combobox.count() == 0:
            baudrates = ['9600', '19200', '38400', '57600', '115200', '230400',
                        '460800', '500000', '576000', '921600', '1000000', '1500000']
            for baudrate in baudrates:
                self.baudrate_combobox.addItem(baudrate, baudrate)
        if not self.serial_port_name_last_success and not self.serial_port_name_current_choose:
            self._load_settings()
    
    @pyqtSlot(int)
    def combox_index_changed(self, index) -> None:
        self.serial_port_name_current_choose = self.port_names[index]

    def _color_text(self, text: str, color: QColor) -> str:
        # red_text = "<span style=\" font-size:8pt; font-weight:600; color:#ff0000;\" >"
        tmp_color = f'{color.red():02x}{color.green():02x}{color.blue():02x}'
        red_text = f'<span style=" color:#{tmp_color};" >'
        red_text += text
        red_text += "</span>"
        return red_text

    def show_result(self, text: str, result_code: HI10.ReturnCode) -> None:
        try:
            self.line_cnt += 1
        except AttributeError as e:
            self.line_cnt = 1
        result_colors = [QColorConstants.DarkGreen, QColorConstants.Black, QColorConstants.DarkRed, QColorConstants.DarkYellow]
        current_time = time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(int(round(time.time()*1000))/1000))
        info = self._color_text(f'{self.line_cnt}:[{current_time}]{text}', result_colors[result_code])
        # self.received_textedit.appendPlainText(info)
        self.received_textedit.appendHtml(info)

    @property
    def port(self) -> str:
        """Return the current serial port."""
        return self.port_combobox.currentData()

    @property
    def baudrate(self) -> str:
        """Return the current serial baudrate."""
        return self.baudrate_combobox.currentText()

    @asyncClose
    async def closeEvent(self, event: QCloseEvent) -> None:
        """Handle Close event of the Widget."""
        self.on_disconnect_btn_pressed()

        self._save_settings()
        # wait speaker exit
        self.speaker.stop()
        self.speaker.wait()
        event.accept()
        os._exit(0)


    def timerEvent(self, event):
        timer_id = event.timerId()
        if timer_id == self.serial_daemon_timer_id:
            signal_blocker0 = QSignalBlocker(self.port_combobox)
            signal_blocker1 = QSignalBlocker(self.baudrate_combobox)
            self.update_com_ports()


    # @handleUIState
    @asyncSlot()
    async def is_module_ready(self) -> bool:
        ret_code, result = await self.hi10.get_module_status(timeout_second = 1)
        if ret_code == HI10.ReturnCode.SUCCESS:
            assert isinstance(result, Reply.ReplyGetStatus)
            if result.status == Reply.ReplyGetStatus.ModuleState.MS_STANDBY:
                return True
            elif result.status == Reply.ReplyGetStatus.ModuleState.MS_BUSY:
                self.show_result(self._translate('LogTextEdit', 'module is busy, please wait for a while'), HI10.ReturnCode.ERROR)
                return False
            elif result.status == Reply.ReplyGetStatus.ModuleState.MS_ERROR:
                self.show_result(self._translate('LogTextEdit', 'calibration data error!'), HI10.ReturnCode.ERROR)
                return True
            else:
                module_state = Reply.ReplyGetStatus.ModuleState.module_state_str(result.status)
                self.show_result(module_state, HI10.ReturnCode.ERROR)
                return False
        else:
            self.show_result(self._translate('LogTextEdit','''module is not ready! \n\r
            check serial port configuration'''), ret_code)
            # self.show_result('模组无响应! \n \
            # (1)请检查串口连接；(2)请检查是否模组处于加密模式，如果处于加密模式点击调试->删除加密key，然后重启模组', ret_code)
            return False

    @handleUIState
    @asyncSlot()
    async def on_action_power_down_triggered(self) -> None:
        ret_code = await self.hi10.power_down(timeout_second=3)
        if ret_code[0] == self.hi10.ReturnCode.SUCCESS:
            self.show_result(self._translate('LogTextEdit', f'send power down success'), ret_code[0])
        else:
            self.show_result(self._translate('LogTextEdit', f'send power down failed'), ret_code[0])

    @handleUIState
    @asyncSlot()
    async def on_action_get_logfiles_triggered(self) -> None:
        if not self.hi10:
            self.show_result(self._translate('LogTextEdit', 'Not connected to device'), HI10.ReturnCode.ERROR)
            return
            
        try:
            # 直接尝试获取日志文件，不先查询大小
            self.show_result(self._translate('LogTextEdit', 'Downloading log file...'), HI10.ReturnCode.INFO)
            ret, log_data = await self.hi10.get_logfile(timeout_second=20)
            
            if ret == HI10.ReturnCode.SUCCESS:
                if log_data and len(log_data) > 0:
                    # 保存日志文件
                    log_filename = self._save_file_with_timestamp(log_data, "logfile", "txt", "log")
                    self.show_result(self._translate('LogTextEdit', f'Log file saved: {log_filename} ({len(log_data)} bytes)'), HI10.ReturnCode.SUCCESS)
                    
                    # 显示日志预览
                    self._show_log_preview(log_data)
                else:
                    self.show_result(self._translate('LogTextEdit', 'No log data available'), HI10.ReturnCode.WARNING)
            else:
                self.show_result(self._translate('LogTextEdit', f'Failed to download log file: {ret}'), HI10.ReturnCode.ERROR)
                
        except Exception as e:
            self._handle_exception('Log retrieval failed', e)

   

    # @handleUIState
    async def check_module_status(self) -> bool:
        module_ready = await self.is_module_ready()
        if module_ready:
            self.enable_ui(self.all_func_ui)
            return True
        else:
            self.disable_ui(self.all_func_ui)
            #excluding
            self.menuDebug.setEnabled(True)
            self.action_delete_key.setEnabled(True)
            self.disconnect_btn.setEnabled(True)
            return False

    @pyqtSlot()
    def on_action_connect_triggered(self) -> None:
        asyncio.ensure_future(self.on_connect_btn_pressed())

    @asyncSlot()
    async def on_connect_btn_pressed(self) -> None:
        """Open serial connection to the specified port."""
        if self.serial_port.is_open:
            self.serial_port.close()

        try:
            self.serial_port = serial.Serial(self.port, int(self.baudrate), parity=serial.PARITY_NONE, stopbits=serial.STOPBITS_ONE, bytesize=serial.EIGHTBITS, timeout=0)
        except Exception as e:
            self.show_error_message(str(e))
            return

        if self.serial_port.is_open:
            #
            self.serial_wrapper = SerialWraper(self.serial_port)
            self.serial_wrapper.signal.serial_error.connect(self.on_disconnect_btn_pressed)
            self.hi10 = HI10(self.serial_wrapper)
            self.hi10.rcv_data_signal.signal.connect(self.on_rcv_data)
            self.hi10.notify_signal.face_state_signal.connect(self.on_notify)
            # connect to hi10
            self.hi10.connect()
            self.connect_btn.setVisible(False)
            self.disconnect_btn.setVisible(True)
            self.port_combobox.setEnabled(False)
            self.baudrate_combobox.setEnabled(False)
            #
            self.action_connect.setVisible(False)
            self.action_disconnect.setVisible(True)
            self.action_delete_key.setEnabled(True)
            init_status = await self.check_module_status()
            if init_status == False:
                self.show_dialog(self._translate('Dialog', '''connect module failed:\n\r
                                                1.please confirm serial connection is OK'''))
                # self.show_dialog("无法连接模组: \n\r 1. 请确认串口连接；\n\r 2. 未从加密模式退出，请重启模组")
                self.enable_first_level_menu_ui(self.first_level_menu_ui)
                self.on_disconnect_btn_pressed()
                return

            self.enable_ui(self.all_func_ui)
            self.recognize_times_lineedit.setEnabled(False)
            self.snap_btn.setEnabled(True)
            self.read_raw_btn.setEnabled(True)
            self.read_alg_btn.setEnabled(True)
            self.show_result(self._translate('LogTextEdit','connect successfully'), HI10.ReturnCode.SUCCESS)
            self.serial_port_name_last_success = self.port
            self.serial_port_name_current_choose = ''
        else:
            self.show_dialog(self._translate('Dialog','open serial failed'))
            return
        # kill daemon timer
        if self.serial_daemon_timer_id != None:
            self.killTimer(self.serial_daemon_timer_id)
            self.serial_daemon_timer_id = None
        # we get_version_info to distinguish product type. example: L18, L18_MX, L18_MINI, HAT001-FW-AI01  ...
        await self.get_version_info()
        self.status_label.setText(self.version_info)
        self.__cur_json_cfg_dict = {}

    @pyqtSlot()
    def on_action_disconnect_triggered(self) -> None:
        asyncio.ensure_future(self.on_disconnect_btn_pressed())

    @pyqtSlot()
    def on_disconnect_btn_pressed(self) -> None:
        """Close current serial connection."""

        if self.hi10 is not None:
            self.hi10.disconnect()
            self.hi10 = None
        # close serial port
        if self.serial_port.is_open:
            self.serial_port.close()

        # 统一禁用所有功能UI
        self.disable_ui(self.all_func_ui)
        
        # 重新启用连接相关的UI
        if hasattr(self, "port_combobox"):
            self.port_combobox.setEnabled(True)
        if hasattr(self, "baudrate_combobox"):
            self.baudrate_combobox.setEnabled(True)
        if hasattr(self, "connect_btn"):
            self.connect_btn.setVisible(True)
        if hasattr(self, "disconnect_btn"):
            self.disconnect_btn.setVisible(False)
        if hasattr(self, "action_connect"):
            self.action_connect.setVisible(True)
        if hasattr(self, "action_disconnect"):
            self.action_disconnect.setVisible(False)

        if self.module_status_monitor_task_running:
            self.module_status_monitor_task_running = False
        # restart daemon timer
        self.serial_daemon_timer_id = self.startTimer(1500)
        self.show_result(self._translate('LogTextEdit', 'disconnect'), HI10.ReturnCode.INFO)
        self.status_label.setText('')

    @pyqtSlot()
    def on_action_recognize_triggered(self) -> None:
        asyncio.ensure_future(self.on_recognize_btn_pressed())

    # @timer
    @handleUIState
    @asyncSlot()
    async def on_recognize_btn_pressed(self) -> None:
        self.left_times_info_label.setVisible(True)
        # replace horizontalLayout_3 second control widget
        self.recognize_times_lineedit.setVisible(False)
        self.stop_recognize_btn.setEnabled(True)
        #
        if self.cont_recognize_checkBox.checkState() == Qt.Checked:
            num_str = self.recognize_times_lineedit.text()
            try:
                counter = int(num_str, base=10)
            except Exception as e:
                self.show_error_message(self._translate('MainWindow', f'wrong parament format'))
                return
        else:
            counter = 1
        recognize_success_cnt = 0
        recognize_unknown_failed_cnt = 0
        recognize_time_out_failed_cnt = 0
        power_down = 0
        timeout = 10
        for index in range(0, counter):
            self.left_times_info_label.setText(self._translate('MainWindow', f"left times: ") + f'{counter-index-1}')
            if self.recognize_need_stop:
                break
            tic = time.perf_counter()
            recognize_ret, recognize_result = await self.hi10.label_recog(timeout_second = timeout)
            toc = time.perf_counter()
            elapsed_time = toc - tic
            
            if self.action_autocapture.isChecked():
                logger.info(f'auto capture: {self.action_autocapture.isChecked()}')
                await self._auto_capture_algorithm_image()
                

            if self.action_time_statistic.isChecked():
                self.show_result(self._translate('LogTextEdit', f'recognize time') + f': {elapsed_time*1000:0.4f} ms', HI10.ReturnCode.INFO)
            if recognize_ret == HI10.ReturnCode.SUCCESS:
                recognize_success_cnt += 1
                self.show_result(recognize_result[2], recognize_ret)
                self.speaker.say_emergency(self._translate('Speaker', 'Recognition successful'))
                # result_str = recognize_result
            else:
                show_msg = recognize_result[2]
                self.show_result(show_msg, recognize_ret)
                self.speaker.say_emergency(self._translate('Speaker', 'Recognition failed'))
        if counter > 1:
            #  TODO: 统计结果，分类显示
            result_str = self._translate('LogTextEdit', f'Recognition completed {counter} times: {recognize_success_cnt} successful')
            result_str += self._translate('LogTextEdit', f', {recognize_unknown_failed_cnt} unknown users') if recognize_unknown_failed_cnt > 0 else ""
            result_str += self._translate('LogTextEdit', f', {recognize_time_out_failed_cnt} timeouts') if recognize_time_out_failed_cnt > 0 else ""
            self.show_result(result_str, HI10.ReturnCode.INFO)
        self.recognize_need_stop = False
        self.stop_recognize_btn.setEnabled(False)
        self.left_times_info_label.setVisible(False)
        self.recognize_times_lineedit.setVisible(True)
        self.cont_recognize_checkBox.setVisible(True)

    @asyncSlot()
    async def on_stop_recognize_btn_pressed(self) -> None:
        self.recognize_need_stop = True
        self.show_result(self._translate('LogTextEdit', 'Stopping recognition, disabling label state notifications and sending reset command...'), HI10.ReturnCode.INFO)
        
        if not self.hi10:
            self.show_result(self._translate('LogTextEdit', 'Not connected to device'), HI10.ReturnCode.ERROR)
            return
        
        # 停止处理NID_LABEL_STATE指令
        self.hi10.disable_label_state_notifications()
        self.show_result(self._translate('LogTextEdit', 'Label state notifications disabled'), HI10.ReturnCode.INFO)
        
        # 发送reset指令来中断当前的验证过程
        self.show_result(self._translate('LogTextEdit', 'Sending reset command...'), HI10.ReturnCode.INFO)
        try:
            ret_code, result = await self.hi10.reset(timeout_second=3)
            if ret_code == HI10.ReturnCode.SUCCESS:
                self.show_result(self._translate('LogTextEdit', 'Reset command sent successfully'), HI10.ReturnCode.SUCCESS)
            else:
                self.show_result(self._translate('LogTextEdit', 'Failed to send reset command'), HI10.ReturnCode.WARNING)
        except Exception as e:
            self.show_result(self._translate('LogTextEdit', f'Error sending reset command: {str(e)}'), HI10.ReturnCode.ERROR)
        
        # 等待验证循环结束
        timeout_counter = 0
        while self.recognize_need_stop and timeout_counter < 100:  # 最多等待1秒
            await asyncio.sleep(0.01)
            timeout_counter += 1
        
        if timeout_counter >= 100:
            self.show_result(self._translate('LogTextEdit', 'Recognition stop timeout, forcing stop'), HI10.ReturnCode.WARNING)
            self.recognize_need_stop = False

    def __get_tool_version(self) -> None:
        tool_name = "HI10 APP"
        version_str = 'v1.0.0'
        app_name = 'HI10 APP'
        return version_str, tool_name, app_name


    @asyncSlot()
    async def get_version_info(self) -> None:
        software_version = self._translate('Dialog', 'Tool version :') + self.__tool_version
        software_version = software_version.replace("\n", "")
        fw_version = ''
        lib_version = ''
        if self.serial_port.is_open:
            timeout = 1
            ret_code, result = await self.hi10.get_firmware_version(timeout)
            if(ret_code == HI10.ReturnCode.SUCCESS):
                fw_version = self._translate('Dialog', 'FW version :') + result.version_str + '\n'
            ret_code, result = await self.hi10.get_lib_model_version(timeout)
            if(ret_code == HI10.ReturnCode.SUCCESS):
                lib_version = result.version_str.replace('\x00', '').strip('\n')
                lib_version = "算法版本:" + lib_version
        lib_version = '    ' + lib_version if lib_version != '' else ''
        fw_version = fw_version.replace('\x00', '').strip('\n')
        lib_version = lib_version.replace('\x00', '').strip('\n')
        self.version_info = '         ' + software_version + '    ' + fw_version + lib_version

    @asyncClose
    @pyqtSlot()
    async def on_action_about_triggered(self) -> None:
        software_version = '\n' + self._translate('Dialog', 'software version is ') + self.__tool_version
        fw_version = ''
        cfg_file_version = ""
        if self.serial_port.is_open:
            timeout = 1
            ret_code, result = await self.hi10.get_firmware_version(timeout)
            if(ret_code == HI10.ReturnCode.SUCCESS):
                fw_version = self._translate('Dialog', 'FW version is ') + result.version_str + '\n'
        self.show_dialog(self._translate('Dialog', f'HI10 APP ') + f'{software_version}' + f'{fw_version}')

    @pyqtSlot()
    def on_action_Open_OTA_Window_triggered(self) -> None:
        if self.is_ota_window_created == False:
            self.otawindow = OTAWindow(self.hi10, self.serial_port)
            self.is_ota_window_created = True
            self.otawindow.config_serial_signal.baudrate.connect(self.change_serial_baudrate, Qt.DirectConnection)
            self.otawindow.close_window_signal.signal.connect(self.on_window_close)
            self.otawindow.update_version_signal.signal.connect(self.on_update_version_signal)
            self.otawindow.show()
            self.disable_ui(self.all_func_ui)

    @pyqtSlot()
    def on_action_Open_SendFile_Window_triggered(self) -> None:
        self.sendimagewindow = SendFileWindow(self.hi10)
        self.sendimagewindow.close_window_signal.signal.connect(self.on_window_close)
        self.sendimagewindow.show()
        self.disable_ui(self.all_func_ui)

    @pyqtSlot()
    def on_window_close(self) -> None:
        self.is_ota_window_created = False
        self.enable_ui(self.all_func_ui)


    def on_update_version_signal(self):
        time.sleep(0.1)
        async def _update_version():
            await self.get_version_info()
            self.status_label.setText(self.version_info)
            # 获取日志大小
            ret_size, size_result = await self.hi10._get_logfile_size(timeout_second=3)
            if ret_size == HI10.ReturnCode.SUCCESS and size_result.size > 0:
                # 获取日志内容
                ret, result = await self.hi10.get_logfile(timeout_second=5)
                self.__cur_json_cfg_dict = {}
                if ret == HI10.ReturnCode.SUCCESS and result:
                    self.__cur_json_cfg_dict = json_bytes_transform_json_dict(result)
        f = asyncio.ensure_future(_update_version())
        while not f.done():
            QApplication.instance().processEvents()

    

    @handleUIState
    @asyncSlot()
    async def on_get_version_btn_pressed(self) -> None:
        """获取版本信息"""
        if not self.hi10:
            self.show_result(self._translate('LogTextEdit', 'Not connected to device'), HI10.ReturnCode.ERROR)
            return
        
        self.show_result(self._translate('LogTextEdit', 'Getting firmware version...'), HI10.ReturnCode.INFO)
        ret_code, result = await self.hi10.get_firmware_version(timeout_second=3)
        
        if ret_code == HI10.ReturnCode.SUCCESS:
            fw_version = result.version_str.replace('\x00', '').strip('\n')
            self.show_result(self._translate('LogTextEdit', f'Firmware version: {fw_version}'), HI10.ReturnCode.SUCCESS)
        else:
            self.show_result(self._translate('LogTextEdit', 'Failed to get firmware version'), HI10.ReturnCode.ERROR)
        
        self.show_result(self._translate('LogTextEdit', 'Getting algorithm version...'), HI10.ReturnCode.INFO)
        ret_code, result = await self.hi10.get_lib_model_version(timeout_second=3)
        
        if ret_code == HI10.ReturnCode.SUCCESS:
            lib_version = result.version_str.replace('\x00', '').strip('\n')
            self.show_result(self._translate('LogTextEdit', f'Algorithm version: {lib_version}'), HI10.ReturnCode.SUCCESS)
        else:
            self.show_result(self._translate('LogTextEdit', 'Failed to get algorithm version'), HI10.ReturnCode.ERROR)

    @handleUIState
    @asyncSlot()
    async def on_get_log_size_btn_pressed(self) -> None:
        """获取日志大小"""
        if not self.hi10:
            self.show_result(self._translate('LogTextEdit', 'Not connected to device'), HI10.ReturnCode.ERROR)
            return
        
        self.show_result(self._translate('LogTextEdit', 'Getting log size...'), HI10.ReturnCode.INFO)
        
        # 获取日志文件大小
        ret_code, result = await self.hi10._get_logfile_size(timeout_second=3)
        if ret_code == HI10.ReturnCode.SUCCESS:
            log_size = result.size
            self.show_result(self._translate('LogTextEdit', f'Log file size: {log_size} bytes'), HI10.ReturnCode.SUCCESS)
        else:
            self.show_result(self._translate('LogTextEdit', 'Failed to get log file size'), HI10.ReturnCode.ERROR)
        


    @handleUIState
    @asyncSlot()
    async def on_get_threshold_btn_pressed(self) -> None:
        """获取阈值设置"""
        if not self.hi10:
            self.show_result(self._translate('LogTextEdit', 'Not connected to device'), HI10.ReturnCode.ERROR)
            return
        
        self.show_result(self._translate('LogTextEdit', 'Getting threshold level...'), HI10.ReturnCode.INFO)
        
        # 使用协议直接获取阈值等级
        ret_code, result = await self.hi10.get_threshold_level(timeout_second=3)
        if ret_code == HI10.ReturnCode.SUCCESS:
            threshold_level = result.threshold_level
            self.show_result(self._translate('LogTextEdit', f'Current threshold level: {threshold_level}'), HI10.ReturnCode.SUCCESS)
        else:
            self.show_result(self._translate('LogTextEdit', 'Failed to get threshold level'), HI10.ReturnCode.ERROR)

    @handleUIState
    @asyncSlot()
    async def on_get_lib_model_version_btn_pressed(self) -> None:
        """获取算法库版本信息"""
        if not self.hi10:
            self.show_result(self._translate('LogTextEdit', 'Not connected to device'), HI10.ReturnCode.ERROR)
            return
        
        self.show_result(self._translate('LogTextEdit', 'Getting algorithm library version...'), HI10.ReturnCode.INFO)
        ret_code, result = await self.hi10.get_lib_model_version(timeout_second=3)
        
        if ret_code == HI10.ReturnCode.SUCCESS:
            lib_version = result.version_str.replace('\x00', '').strip('\n')
            self.show_result(self._translate('LogTextEdit', f'Algorithm library version: {lib_version}'), HI10.ReturnCode.SUCCESS)
        else:
            self.show_result(self._translate('LogTextEdit', 'Failed to get algorithm library version'), HI10.ReturnCode.ERROR)

    @handleUIState
    @asyncSlot()
    async def on_set_threshold_btn_pressed(self) -> None:
        """设置阈值等级"""
        if not self.hi10:
            self.show_result(self._translate('LogTextEdit', 'Not connected to device'), HI10.ReturnCode.ERROR)
            return
        
        # 弹出对话框让用户输入阈值等级
        from PyQt5.QtWidgets import QInputDialog
        threshold_level, ok = QInputDialog.getInt(
            self, 
            self._translate('Dialog', 'Set Threshold Level'),
            self._translate('Dialog', 'Enter threshold level (0-4):'),
            value=2, min=0, max=4
        )
        
        if ok:
            self.show_result(self._translate('LogTextEdit', f'Setting threshold level to: {threshold_level}'), HI10.ReturnCode.INFO)
            ret_code, result = await self.hi10.set_threshold_level(threshold_level, timeout_second=3)
            
            if ret_code == HI10.ReturnCode.SUCCESS:
                self.show_result(self._translate('LogTextEdit', f'Threshold level set successfully'), HI10.ReturnCode.SUCCESS)
            else:
                self.show_result(self._translate('LogTextEdit', 'Failed to set threshold level'), HI10.ReturnCode.ERROR)

    @handleUIState
    @asyncSlot()
    async def on_get_status_btn_pressed(self) -> None:
        """获取模块状态"""
        if not self.hi10:
            self.show_result(self._translate('LogTextEdit', 'Not connected to device'), HI10.ReturnCode.ERROR)
            return
        
        self.show_result(self._translate('LogTextEdit', 'Getting module status...'), HI10.ReturnCode.INFO)
        ret_code, result = await self.hi10.get_module_status(timeout_second=3)
        
        if ret_code == HI10.ReturnCode.SUCCESS:
            status_str = Reply.ReplyGetStatus.ModuleState.module_state_str(result.status)
            self.show_result(self._translate('LogTextEdit', f'Module status: {status_str}'), HI10.ReturnCode.SUCCESS)
        else:
            self.show_result(self._translate('LogTextEdit', 'Failed to get module status'), HI10.ReturnCode.ERROR)

    @handleUIState
    @asyncSlot()
    async def on_reset_btn_pressed(self) -> None:
        """重置模块"""
        if not self.hi10:
            self.show_result(self._translate('LogTextEdit', 'Not connected to device'), HI10.ReturnCode.ERROR)
            return
        
        self.show_result(self._translate('LogTextEdit', 'Resetting module...'), HI10.ReturnCode.INFO)
        ret_code, result = await self.hi10.reset(timeout_second=3)
        
        if ret_code == HI10.ReturnCode.SUCCESS:
            self.show_result(self._translate('LogTextEdit', 'Module reset successfully'), HI10.ReturnCode.SUCCESS)
        else:
            self.show_result(self._translate('LogTextEdit', 'Failed to reset module'), HI10.ReturnCode.ERROR)

    @handleUIState
    @asyncSlot()
    async def on_get_raw_image_size_btn_pressed(self) -> None:
        """获取RAW图像大小"""
        if not self.hi10:
            self.show_result(self._translate('LogTextEdit', 'Not connected to device'), HI10.ReturnCode.ERROR)
            return
        
        self.show_result(self._translate('LogTextEdit', 'Getting raw image size...'), HI10.ReturnCode.INFO)
        ret_code, result = await self.hi10._get_raw_imgsize(timeout_second=3)
        
        if ret_code == HI10.ReturnCode.SUCCESS:
            size = result.size
            self.show_result(self._translate('LogTextEdit', f'Raw image size: {size} bytes'), HI10.ReturnCode.SUCCESS)
        else:
            self.show_result(self._translate('LogTextEdit', 'Failed to get raw image size'), HI10.ReturnCode.ERROR)

    @handleUIState
    @asyncSlot()
    async def on_upload_raw_image_btn_pressed(self) -> None:
        """上传RAW图像"""
        if not self.hi10:
            QMessageBox.warning(self, "Warning", "Not connected to device.")
            return

        module_ready = await self.is_module_ready()
        if not module_ready:
            self.show_result(self._translate('LogTextEdit', 'Module is not ready for image capture.'), HI10.ReturnCode.ERROR)
            return

        await self._capture_and_display_image("raw")

    @handleUIState
    @asyncSlot()
    async def on_get_alg_image_size_btn_pressed(self) -> None:
        """获取算法图像大小"""
        if not self.hi10:
            self.show_result(self._translate('LogTextEdit', 'Not connected to device'), HI10.ReturnCode.ERROR)
            return
        
        self.show_result(self._translate('LogTextEdit', 'Getting algorithm image size...'), HI10.ReturnCode.INFO)
        ret_code, result = await self.hi10._get_alg_imgsize(timeout_second=3)
        
        if ret_code == HI10.ReturnCode.SUCCESS:
            size = result.size
            self.show_result(self._translate('LogTextEdit', f'Algorithm image size: {size} bytes'), HI10.ReturnCode.SUCCESS)
        else:
            self.show_result(self._translate('LogTextEdit', 'Failed to get algorithm image size'), HI10.ReturnCode.ERROR)

    @handleUIState
    @asyncSlot()
    async def on_upload_alg_image_btn_pressed(self) -> None:
        """上传算法图像"""
        if not self.hi10:
            QMessageBox.warning(self, "Warning", "Not connected to device.")
            return

        module_ready = await self.is_module_ready()
        if not module_ready:
            self.show_result(self._translate('LogTextEdit', 'Module is not ready for image capture.'), HI10.ReturnCode.ERROR)
            return
        
        await self._capture_and_display_image("alg")

    @handleUIState
    @asyncSlot()
    async def on_send_cmd_pressed(self) -> None:
        """发送自定义命令"""
        if not self.hi10:
            self.show_result(self._translate('LogTextEdit', 'Not connected to device'), HI10.ReturnCode.ERROR)
            return
        
        cmd_text = self.cmd_text.text().strip()
        if not cmd_text:
            self.show_result(self._translate('LogTextEdit', 'Please enter a command'), HI10.ReturnCode.WARNING)
            return
        
        self.show_result(self._translate('LogTextEdit', f'Sending command: {cmd_text}'), HI10.ReturnCode.INFO)
        
        # 这里可以根据需要解析和执行特定的命令
        # 目前作为示例，显示命令已发送
        cmd_str = self.cmd_text.text()
        if ',' in cmd_str:
            cmd, param = cmd_str.split(',')
            cmd = cmd.strip()
            param = param.strip()
            try:
                cmd = int(cmd)
            except ValueError as e:
                cmd = 0x00
                param = cmd_str.strip()
        else:
            cmd = 0x00
            param = cmd_str.strip()
        try:
            self.cmd_text.append_history_string(cmd_str)
        except Exception as e:
            pass
        print(cmd_str)


        cmd_len = len(cmd_str)
        _timeout = 10
   
  
        if cmd == CmdParse.CmdCode.EXEC_SHELL_CMDS:
            if "reboot" == param:
                # reboot命令特殊处理：发送命令但不等待响应（设备会重启）
                try:
                    # 直接发送reboot命令，使用最短超时
                    await self.hi10.send_cmd_parse(cmd, cmd_len, param, timeout_second=0.1)
                    # 不管返回结果如何，都认为命令已发送（因为设备会重启无法响应）
                    self.show_result(f"Reboot command sent successfully", HI10.ReturnCode.SUCCESS)
                    self.show_result(f"Device is rebooting, please wait...", HI10.ReturnCode.INFO)
                except Exception as e:
                    # 即使出现异常，也可能是因为设备重启导致的，所以仍然提示成功
                    self.show_result(f"Reboot command sent (device restarting)", HI10.ReturnCode.SUCCESS)

                # 等待设备重启完成（更长的等待时间）
                await asyncio.sleep(3)  # 等待3秒让设备开始重启

                # 尝试重新连接，最多等待30秒
                for i in range(6):  # 6次尝试，每次5秒
                    self.show_result(f"Waiting for device to restart... ({i+1}/6)", HI10.ReturnCode.INFO)
                    await asyncio.sleep(5)

                    try:
                        # 重新建立串口连接
                        if not self.serial_port.is_open:
                            self.serial_port.open()
                            self.hi10.connect()

                        ready_ret, _ = await self.hi10.wait_ready(3)
                        if ready_ret == HI10.ReturnCode.SUCCESS:
                            await self.get_version_info()
                            self.status_label.setText(self.version_info)
                            self.show_result(f"Device restarted successfully", HI10.ReturnCode.SUCCESS)
                            return
                    except Exception as e:
                        print(f"Attempt {i+1} failed: {e}")
                        # 如果连接失败，关闭串口准备下次重试
                        try:
                            if self.serial_port.is_open:
                                self.hi10.disconnect()
                                self.serial_port.close()
                        except:
                            pass
                        continue

                # 如果30秒后仍未连接成功
                self.show_result(f"Device restart timeout. Please manually reconnect.", HI10.ReturnCode.WARNING)
                return
            else:
                # 其他shell命令的处理
                ret_code, result = await self.hi10.send_cmd_parse(cmd, cmd_len, param, timeout_second=_timeout)
                ready_ret, _ = await self.hi10.wait_ready(5)
                if ready_ret != HI10.ReturnCode.SUCCESS:
                    get_ready_ret = await self.is_module_ready()
                    self.show_result(f"send cmd: {cmd_str}, {result}", HI10.ReturnCode.SUCCESS if get_ready_ret else HI10.ReturnCode.ERROR)
                    return
                else:
                    await self.get_version_info()
                    self.status_label.setText(self.version_info)
                    self.show_result(f"send cmd: {cmd_str}, success", HI10.ReturnCode.SUCCESS)
                    return
        
    
            
           


    @pyqtSlot()
    def on_cmd_text_returnPressed(self) -> None:
        asyncio.ensure_future(self.on_send_cmd_pressed())

    # MenuFunction 按钮事件处理器
    @pyqtSlot()
    def on_action_label_recog_triggered(self) -> None:
        """标签识别菜单项"""
        asyncio.ensure_future(self.on_recognize_btn_pressed())

    @pyqtSlot()
    def on_action_get_version_triggered(self) -> None:
        """获取版本信息菜单项"""
        asyncio.ensure_future(self.on_get_version_btn_pressed())

    @pyqtSlot()
    def on_action_get_lib_model_version_triggered(self) -> None:
        """获取算法库版本菜单项"""
        asyncio.ensure_future(self.on_get_lib_model_version_btn_pressed())

    @pyqtSlot()
    def on_action_get_threshold_triggered(self) -> None:
        """获取阈值菜单项"""
        asyncio.ensure_future(self.on_get_threshold_btn_pressed())

    @pyqtSlot()
    def on_action_set_threshold_triggered(self) -> None:
        """设置阈值菜单项"""
        asyncio.ensure_future(self.on_set_threshold_btn_pressed())

    @pyqtSlot()
    def on_action_get_status_triggered(self) -> None:
        """获取状态菜单项"""
        asyncio.ensure_future(self.on_get_status_btn_pressed())

    @pyqtSlot()
    def on_action_get_log_size_triggered(self) -> None:
        """获取日志大小菜单项"""
        asyncio.ensure_future(self.on_get_log_size_btn_pressed())

    @pyqtSlot()
    def on_action_reset_triggered(self) -> None:
        """重置模块菜单项"""
        asyncio.ensure_future(self.on_reset_btn_pressed())

    @pyqtSlot()
    def on_action_get_raw_image_size_triggered(self) -> None:
        """获取RAW图像大小菜单项"""
        asyncio.ensure_future(self.on_get_raw_image_size_btn_pressed())

    @pyqtSlot()
    def on_action_upload_raw_image_triggered(self) -> None:
        """上传RAW图像菜单项"""
        asyncio.ensure_future(self.on_upload_raw_image_btn_pressed())

    @pyqtSlot()
    def on_action_get_alg_image_size_triggered(self) -> None:
        """获取算法图像大小菜单项"""
        asyncio.ensure_future(self.on_get_alg_image_size_btn_pressed())

    @pyqtSlot()
    def on_action_upload_alg_image_triggered(self) -> None:
        """上传算法图像菜单项"""
        asyncio.ensure_future(self.on_upload_alg_image_btn_pressed())

    @pyqtSlot()
    def respond_window_close(self) -> None:
        self.enable_ui(self.all_func_ui)

    @pyqtSlot()
    def on_action_readimage_triggered(self) -> None:
        asyncio.ensure_future(self.on_readimg_btn_pressed())

    async def _auto_capture_algorithm_image(self) -> None:
        """自动抓取算法图像的内部方法，不使用UI状态装饰器"""
        if not self.hi10:
            self.show_result(self._translate('LogTextEdit', 'Not connected to device'), HI10.ReturnCode.ERROR)
            return

        self.show_result(self._translate('LogTextEdit', 'Auto capturing algorithm image...'), HI10.ReturnCode.INFO)
        await self._capture_and_display_image("alg")

    async def _capture_and_display_image(self, image_type: str):
        """抓取图像并在UI中显示"""
        # 使用核心抓图函数
        ret_code, image_data, pixmap = await read_image_internal(self.hi10, image_type)

        if ret_code == self.hi10.ReturnCode.SUCCESS and pixmap:
            # 显示图像
            display_size = self._get_adaptive_display_size()
            scaled_pixmap = pixmap.scaled(
                display_size,
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            self.image_display_label.setPixmap(scaled_pixmap)
            self.image_display_label.setVisible(True)

            # 在保存时检测图像格式
            image_format = "raw"  # 默认格式
            if image_data:
                detected_format = detect_image_format(image_data)
                if detected_format:
                    image_format = detected_format

            # 保存原始数据和PNG格式
            saved_files = save_image_data(pixmap, image_data, f"{image_type}_image", f"{image_type}_image", image_format)

            # 显示保存结果
            if saved_files:
                for filepath in saved_files:
                    self.received_textedit.appendPlainText(f"{image_type} image saved to {filepath}")
            else:
                self.received_textedit.appendPlainText(f"Failed to save {image_type} image")
        else:
            self.received_textedit.appendPlainText(f"Failed to read {image_type} image")

    @handleUIState
    @asyncSlot()
    async def on_readimg_btn_pressed(self, image_type: str = None) -> None:
        """ReadImg按钮点击处理"""
        if not self.hi10:
            QMessageBox.warning(self, "Warning", "Not connected to device.")
            return

        module_ready = await self.is_module_ready()
        if not module_ready:
            self.show_result(self._translate('LogTextEdit', 'Module is not ready for image capture.'), HI10.ReturnCode.ERROR)
            return

        # 如果没有指定图像类型，弹出对话框让用户选择
        if not image_type:
            from PyQt5.QtWidgets import QInputDialog
            items = ["Raw Image", "Algorithm Image"]
            item, ok = QInputDialog.getItem(self, "Select Image Type",
                                          "Choose image type to read:", items, 0, False)
            if not ok:
                return  # 用户取消了选择

            image_type = "raw" if item == "Raw Image" else "alg"

        await self._capture_and_display_image(image_type)

    @handleUIState
    @asyncSlot()
    async def on_read_raw_btn_pressed(self) -> None:
        """ReadRAW按钮点击处理"""
        if not self.hi10:
            QMessageBox.warning(self, "Warning", "Not connected to device.")
            return

        module_ready = await self.is_module_ready()
        if not module_ready:
            self.show_result(self._translate('LogTextEdit', 'Module is not ready for image capture.'), HI10.ReturnCode.ERROR)
            return

        # 直接调用RAW图像读取
        await self._capture_and_display_image("raw")

    @handleUIState
    @asyncSlot()
    async def on_read_alg_btn_pressed(self) -> None:
        """ReadALG按钮点击处理"""
        if not self.hi10:
            QMessageBox.warning(self, "Warning", "Not connected to device.")
            return

        module_ready = await self.is_module_ready()
        if not module_ready:
            self.show_result(self._translate('LogTextEdit', 'Module is not ready for image capture.'), HI10.ReturnCode.ERROR)
            return

        # 直接调用算法图像读取
        await self._capture_and_display_image("alg")

    @pyqtSlot(int, int)
    def on_rcv_data(self, current: int, total: int) -> None:
        process = f'receive data:{current}/{total}'
        self.show_result(process, HI10.ReturnCode.INFO)
    
    @pyqtSlot()
    def on_continue_recognize_checkbox_statechanged(self) -> None:
        if self.cont_recognize_checkBox.checkState() == Qt.Checked:
            self.recognize_times_lineedit.setEnabled(True)
            self.recognize_times_lineedit.setText("10")
        else:
            self.recognize_times_lineedit.setText("")
            self.recognize_times_lineedit.setEnabled(False)

    @pyqtSlot(tuple, int, int, int)
    def on_notify(self, face_state_tuple: tuple, yaw: int, pitch: int, roll: int) -> None:
        if GlobalConfigParam.CFG_ENABLE_STATUS_REPORT:
            tips = str()
            state_code, tips = face_state_tuple
            if state_code == 'FACE_STATE_DIRECTION_ERROR':
                # TODO: add a more friendly tips
                tips = self._translate('Speaker', 'Please face the camera directly')
                self.speaker.say_normal(tips)
                self.show_result(f'{state_code}:{tips}', HI10.ReturnCode.WARNING)
            else:
                self.show_result(f'{state_code}:{tips}', HI10.ReturnCode.WARNING)
                self.speaker.say_normal(tips)


    def _enable_ui(self, enable, ui_array) -> None:
        for ui in ui_array:
            if ui is not None:
                ui.setEnabled(enable)

    def enable_ui(self, ui_array) -> None:
        self._enable_ui(True, ui_array)

    def disable_ui(self, ui_array) -> None:
        self._enable_ui(False, ui_array)
    
    def enable_first_level_menu_ui(self, ui_array) -> None:
        for ui in ui_array:
            if ui is not None:
                ui.setEnabled(True)

    @asyncSlot()
    async def baudrate_config(self, baudrate, timeout = 3) -> bool:
        ret_code, result = await self.hi10.config_baudrate(Change_Baudrate_To_Protocol_Number[baudrate], timeout)
        if ret_code == HI10.ReturnCode.SUCCESS:
            self.change_serial_baudrate(baudrate)
            self.show_result(self._translate('LogTextEdit','successfully set baudrate') + f'{baudrate}', ret_code)
            return True
        else:
            self.show_result(self._translate('LogTextEdit','failed to set baudrate') + f'{baudrate}', ret_code)
            return False

    # restore original baudrate
    @pyqtSlot(int)
    def change_serial_baudrate(self, baudrate):
        if self.serial_port.is_open:
            self.hi10.disconnect()
            self.serial_port.close()
        if baudrate == 0:
            #use original baudrate (19200)
            self.serial_port.baudrate = 19200
        else:
            #configure with new baudrate
            self.serial_port.baudrate = baudrate
        self.serial_port.open()
        self.hi10.connect()

        actual_baudrate = str(self.serial_port.baudrate)
        index = self.baudrate_combobox.findData(actual_baudrate)
        if index > -1:
            self.baudrate_combobox.setCurrentIndex(index)

    # TODO optimize following functions
    def show_dialog(self, info: str) -> None:
        mb = QMessageBox()
        mb.setIcon(QMessageBox.Information)
        # mb.setText("This is a message box")
        mb.setInformativeText(info)
        mb.setWindowTitle("Info")
        # mb.setDetailedText("The details are as follows:")
        mb.setStandardButtons(QMessageBox.Ok)
        mb.show()
        # center messagebox to the main window
        rect = self.geometry()
        mb_width = mb.width()
        mb_height = mb.height()
        mb.move(int(rect.left() + (rect.width()-mb_width)/2), int(rect.top() + (rect.height()-mb_height)/2))
        mb.exec_()

    def dialog_ok_cancel(self, info: str) -> None:
        mb = QMessageBox()
        mb.setIcon(QMessageBox.Information)
        mb.setInformativeText(info)
        mb.setWindowTitle("Info")
        mb.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
        mb.show()
        # center messagebox to the main window
        rect = self.geometry()
        mb_width = mb.width()
        mb_height = mb.height()
        mb.move(int(rect.left() + (rect.width()-mb_width)/2), int(rect.top() + (rect.height()-mb_height)/2))

        return mb.exec_()

    def event(self, event):
        #如果有按键按下，并且按键是tab键
        # if event.type() == QEvent.KeyPress and event.key() == Qt.Key_Tab:    
        #     return True
        return QWidget.event(self, event)  


    def keyPressEvent(self, event):
        modifiers = event.modifiers()
        if modifiers == (Qt.ControlModifier|Qt.ShiftModifier) and event.key() == Qt.Key_D:
            print("Ctrl+Shift+D pressed!")
            if True == self.__debug_mode_window.isHidden():
                self.__debug_mode_window.set_whther_support_snap_small_raw_image(self.__is_support_snap_small_raw_image)
                self.__debug_mode_window.show()
            self.releaseKeyboard()
            # self.username_lineedit.releaseKeyboard()
        elif  modifiers == (Qt.ControlModifier|Qt.ShiftModifier):
            print("Ctrl+Shift pressed!")   
            self.grabKeyboard()
            # self.username_lineedit.grabKeyboard()


if __name__ == "__main__":
    # Handle high resolution displays:
    if hasattr(QtCore.Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(QtCore.Qt.AA_EnableHighDpiScaling, True)
    if hasattr(QtCore.Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(QtCore.Qt.AA_UseHighDpiPixmaps, True)
    app = QApplication(sys.argv)
    trans = QTranslator()
    locallization_file = resource_path("localization/hi10_app.qm")
    trans.load(locallization_file)
    app.installTranslator(trans)

    loop = QEventLoop(app)
    asyncio.set_event_loop(loop)

    window = MainWindow()
    window.show()

    with loop:
        sys.exit(loop.run_forever())