#! /usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import asyncio
from src.sync_api import HI10
from src.serialwraper import <PERSON>ialWraper
import serial
from qasync import QEventLoop
from PyQt5.QtWidgets import QApplication

async def main():
    init_baudrate = 19200
    serial_wrapper = SerialWraper(serial.Serial("/dev/ttyUSB0",init_baudrate,timeout=0.5))
    facelock = HI10(serial_wrapper)
    facelock.connect()
    result = await facelock.recognize(timeout_second=20)
    print(result)
    new_baudrate = 460800
    ret_code, result = await facelock.config_baudrate(new_baudrate, timeout_second=5)
    if ret_code == facelock.ReturnCode.SUCCESS:
        facelock.change_baudrate(new_baudrate)
        retcode, image = await facelock.read_recent_image(20)
        # revert to initial baudrate
        ret_code, result = await facelock.config_baudrate(init_baudrate, timeout_second=5)
        if ret_code == facelock.ReturnCode.SUCCESS:
            facelock.change_baudrate(init_baudrate)

        if retcode == HI10.ReturnCode.SUCCESS:
            with open("recent_image.raw", "wb") as f:
                f.write(image)

if __name__ == "__main__":
    app = QApplication([])
    loop = QEventLoop(app)
    asyncio.set_event_loop(loop)

    asyncio.ensure_future(main())

    with loop:
        sys.exit(loop.run_forever())
