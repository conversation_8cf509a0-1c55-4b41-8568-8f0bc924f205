1. 需要翻译的文本中加入以下内容：
    self._translate = QtCore.QCoreApplication.translate
    text_to_be_translated = self._translate('WindowContextInfo','text_to_be_translated')
    其中WindowContextInfo代表'text_to_be_translated'的归属，可以简单认为它代表了要翻译的内容属于哪个组，组名可以随便起，但尽量要有一定意义

2. 使用pylupdate5生成ts文件，一般情况下pylupdate5已经包含在pyqt-tools中
    把所有的包含要修改文本的python文件打包成ts文件，或直接把全部文件打包成一个ts文件：
    pylupdate5 *.py ui/*.py src/*.py -noobsolete -ts localization/hi10_app.ts

3. 去下载一个qtlinguist软件，这个软件在qt-tools中不包含，所以必须单独下载。
    打开aiva_facelock.ts进行翻译

4. 翻译完成后点击file->release生成对应的aiva_facelock.qm文件
    lrelease localization/hi10_app.ts -qm localization/hi10_app.qm

5. 在代码中load该文件
    from PyQt5.QtCore import QTranslator
    trans = QTranslator()
    trans.load('localization/hi10_app.qm')
