# 1.prepair environment
mainly needed lib:
pip install PyQt5 pyserial pyttsx3 qasync asyncio aioify pyinstaller

for encrypt version, should install Cryptodome by ```pip install pycryptodomex```
on windows you should install some build tools before you can successfully
install Cryptodome. you can install ```MS VS Studio Community 2019``` for example.

[refer this link](https://pycryptodome.readthedocs.io/en/latest/src/introduction.html)

# 2.pack and release
```bash
./release.sh
```
# 3. generate py file from ui file
open and modify [MainWindow.ui](../MainWindow.ui) use qtdesigner if you want to change UI
then use pyuic5 to generate [MainWindow.py](../MainWindow.py) which we will use
```bash
pyuic5.exe MainWindow.ui -o MainWindow.py
```

# 4. for windows release issue
```bash
caoju@DESKTOP-545J227 MINGW64 /d/work/face_demo/dist (master)
$ ./AIVA_FaceLock.exe
Traceback (most recent call last):
  File "site-packages\PyInstaller\loader\rthooks\pyi_rth_pkgres.py", line 13, in <module>
  File "C:\Users\<USER>\miniconda3\lib\site-packages\PyInstaller\loader\pyimod03_importers.py", line 623, in exec_module
    exec(bytecode, module.__dict__)
  File "site-packages\pkg_resources\__init__.py", line 86, in <module>
ModuleNotFoundError: No module named 'pkg_resources.py2_warn'

```
[see this link](https://stackoverflow.com/questions/61574984/no-module-named-pkg-resources-py2-warn-pyinstaller) no-module-named-pkg-resources-py2-warn-pyinstaller

pip3 install setuptools --upgrade