# HI10 app

## 1. PC APP work with AI3001E face lock module
### (1) compile and release this app
[see this document](./doc/compile.md)

## 2.configuration
this APP support configuration file to change the behaviour, app will first try to find and parse configuration file at current folder, file
name is ".aiva_facelock.ini", if not found this file, will use default
value and write default config to ".aiva_facelock.ini"

## 3.create executable app
use "./releash.sh" to create AIVA_FaceLock app under "dist" directory.
use "./releash.sh factory" to create Factory tool under "dist" directory.

## 4.software architecture
```
                     .--------.
                     |  UI && |
          .----------|busyness|
         /           | logic  |
        /            '--------'
       /                 |  '---------------.
      |                  |                  v
      |                  |              .---------.
      |                  v              | Speaker |
.------------.      .---------.         '---------'
|  Protocol  |------|SYNC  API|
|   Define   |      '---------'
'------------'           |
      |                  |
      |                  v
      '            .------------.
       \           |ASYNC Serial|
        \..........|  Reader && |
                   |   Writer   |
                   '------------'
```
# miniconda env install list

```c
/*
      conda create --name py37for_facelock_demo python=3.7.7
      conda activate py37for_facelock_demo
      conda install pyserial
      pip install pyqt5
      pip install qasync
      conda install pycryptodomex
      pip install pyttsx3
      pip install pyinstaller==3.5
      pip install psutil
      pip install xlwt
may be need:
      pip install opencv-python==******** opencv-contrib-python==********
      pip install matplotlib
      pip install scipy
      pip install pyaudio
```

# some issue solution

```c
/*
如果出现 ubuntu qt.qpa.plugin: Could not load the Qt platform plugin "xcb" in "" even though it was found.
This application failed to start because no Qt platform plugin could be initialized. Reinstalling the application may fix this problem.
Available platform plugins are: eglfs, linuxfb, minimal, minimalegl, offscreen, vnc, wayland-egl, wayland, wayland-xcomposite-egl, wayland-xcomposite-glx, webgl, xcb.
解决方法: sudo apt-get install libxcb-xinerama0

如果出现 libespeak.so.1: 无法打开共享对象文件：没有那个文件或目录
解决方法: sudo apt-get install espeak
```
# localization

参考[国际化](./localization.md)
