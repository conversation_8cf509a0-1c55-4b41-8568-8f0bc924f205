import numpy as np
import matplotlib.pyplot as plt
import argparse
from pathlib import Path

def read_raw8_image(file_path: str, width: int, height: int) -> np.ndarray:
    """从文件读取RAW8图像数据"""
    try:
        # 读取原始字节数据
        with open(file_path, 'rb') as f:
            raw_data = f.read()
        
        # 验证数据长度
        expected_size = width * height
        raw_data = raw_data[:expected_size]  # 截取到期望大小
        # if len(raw_data) != expected_size:
        #     raise ValueError(f"数据长度不匹配: 文件大小为 {len(raw_data)} 字节，但 width*height={expected_size}")
        
        # 转换为numpy数组
        img_array = np.frombuffer(raw_data, dtype=np.uint8).reshape((height, width))
        return img_array
    except Exception as e:
        print(f"读取图像时出错: {e}")
        return None

def display_image(img_data: np.ndarray, title: str = "RAW8 Image") -> None:
    """显示图像数据"""
    if img_data is None:
        return
    
    plt.figure(figsize=(10, 8))
    plt.imshow(img_data, cmap='gray')  # 使用灰度色彩映射
    plt.title(title)
    plt.axis('on')  # 显示坐标轴以便查看像素尺寸
    plt.colorbar(label='Pixel Value')  # 添加颜色条显示像素值范围
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='显示RAW8格式图像')
    parser.add_argument('--file', help='RAW8图像文件路径')
    parser.add_argument('--width', type=int, required=True, help='图像宽度(像素)')
    parser.add_argument('--height', type=int, required=True, help='图像高度(像素)')
    parser.add_argument('--title', help='图像标题', default='RAW8 Image')
    
    args = parser.parse_args()
    
    # 验证文件是否存在
    file_path = Path(args.file)
    if not file_path.is_file():
        print(f"错误: 文件 '{args.file}' 不存在")
        exit(1)
    
    # 读取并显示图像
    image_data = read_raw8_image(str(file_path), args.width, args.height)
    if image_data is not None:
        display_image(image_data, args.title)    